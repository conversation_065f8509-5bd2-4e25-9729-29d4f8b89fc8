#!/usr/bin/env python3
"""
Test script for academic calendar import functionality.
Tests importing from various file formats and validates the results.
"""

import os
import tempfile
from datetime import date
from academic_calendar_importer import AcademicCalendarImporter
from academic_calendar_events import get_academic_calendar_events


def create_test_ical_file() -> str:
    """Create a test iCal file for import testing."""
    ical_content = """BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Test Calendar//Test Events//EN
CALSCALE:GREGORIAN
METHOD:PUBLISH
BEGIN:VEVENT
UID:test-event-1
DTSTAMP:20250601T120000Z
DTSTART;VALUE=DATE:20250815
SUMMARY:Test Deadline Event
DESCRIPTION:This is a test deadline for registration
CATEGORIES:DEADLINE,REGISTRATION
STATUS:CONFIRMED
TRANSP:TRANSPARENT
END:VEVENT
BEGIN:VEVENT
UID:test-event-2
DTSTAMP:20250601T120000Z
DTSTART;VALUE=DATE:20250901
SUMMARY:Test Holiday Event
DESCRIPTION:Labor Day Holiday Test
CATEGORIES:HOLIDAY
STATUS:CONFIRMED
TRANSP:TRANSPARENT
END:VEVENT
BEGIN:VEVENT
UID:test-event-3
DTSTAMP:20250601T120000Z
DTSTART;VALUE=DATE:20260115
SUMMARY:Test Class Start
DESCRIPTION:Classes begin for test term
CATEGORIES:CLASS_DATE
STATUS:CONFIRMED
TRANSP:TRANSPARENT
END:VEVENT
END:VCALENDAR"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ics', delete=False) as f:
        f.write(ical_content)
        return f.name


def create_test_csv_file() -> str:
    """Create a test CSV file for import testing."""
    csv_content = """title,date,description
"Test Registration Deadline","2025-08-20","Last day to register for test term"
"Test Payment Due","2025-08-25","Payment deadline for test term"
"Test Holiday","2025-09-05","Test holiday - University closed"
"Test Classes Begin","2026-01-20","First day of test classes"
"Test Commencement","2026-05-15","Test graduation ceremony"
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(csv_content)
        return f.name


def test_ical_import():
    """Test importing from iCal file."""
    print("🧪 Testing iCal Import")
    print("-" * 25)
    
    test_file = create_test_ical_file()
    
    try:
        importer = AcademicCalendarImporter()
        existing_events = get_academic_calendar_events()
        
        imported_events, stats = importer.import_from_file(test_file, existing_events)
        
        print(f"📊 Import Results:")
        print(f"  Total processed: {stats['total_processed']}")
        print(f"  Successfully imported: {stats['successful_imports']}")
        print(f"  Failed imports: {stats['failed_imports']}")
        print(f"  Duplicates skipped: {stats['duplicates_skipped']}")
        
        print(f"\n📅 Imported Events:")
        for event in imported_events:
            print(f"  - {event.title} ({event.date}) - {event.event_type.value}")
        
        # Validate results
        assert len(imported_events) == 3, f"Expected 3 events, got {len(imported_events)}"
        assert stats['successful_imports'] == 3, f"Expected 3 successful imports"
        assert stats['failed_imports'] == 0, f"Expected 0 failed imports"
        
        print("✅ iCal import test passed!")
        
    finally:
        os.unlink(test_file)


def test_csv_import():
    """Test importing from CSV file."""
    print("\n🧪 Testing CSV Import")
    print("-" * 25)
    
    test_file = create_test_csv_file()
    
    try:
        importer = AcademicCalendarImporter()
        existing_events = get_academic_calendar_events()
        
        imported_events, stats = importer.import_from_file(test_file, existing_events)
        
        print(f"📊 Import Results:")
        print(f"  Total processed: {stats['total_processed']}")
        print(f"  Successfully imported: {stats['successful_imports']}")
        print(f"  Failed imports: {stats['failed_imports']}")
        print(f"  Duplicates skipped: {stats['duplicates_skipped']}")
        
        print(f"\n📅 Imported Events:")
        for event in imported_events:
            print(f"  - {event.title} ({event.date}) - {event.event_type.value}")
        
        # Validate results
        assert len(imported_events) == 5, f"Expected 5 events, got {len(imported_events)}"
        assert stats['successful_imports'] == 5, f"Expected 5 successful imports"
        assert stats['failed_imports'] == 0, f"Expected 0 failed imports"
        
        print("✅ CSV import test passed!")
        
    finally:
        os.unlink(test_file)


def test_duplicate_detection():
    """Test duplicate detection functionality."""
    print("\n🧪 Testing Duplicate Detection")
    print("-" * 30)
    
    # Create a CSV with events that match existing ones
    duplicate_csv = """title,date,description
"Last day to apply for Fall 1 - 2025","2025-08-05","Duplicate event test"
"CLASSES BEGIN (Monday)","2025-08-18","Another duplicate test"
"New Unique Event","2025-12-25","This should be imported"
"""
    
    test_file = None
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(duplicate_csv)
            test_file = f.name
        
        importer = AcademicCalendarImporter()
        existing_events = get_academic_calendar_events()
        
        imported_events, stats = importer.import_from_file(test_file, existing_events)
        
        print(f"📊 Import Results:")
        print(f"  Total processed: {stats['total_processed']}")
        print(f"  Successfully imported: {stats['successful_imports']}")
        print(f"  Failed imports: {stats['failed_imports']}")
        print(f"  Duplicates skipped: {stats['duplicates_skipped']}")
        
        # Should only import the unique event
        assert len(imported_events) == 1, f"Expected 1 unique event, got {len(imported_events)}"
        assert stats['duplicates_skipped'] == 2, f"Expected 2 duplicates skipped"
        assert imported_events[0].title == "New Unique Event", "Wrong event imported"
        
        print("✅ Duplicate detection test passed!")
        
    finally:
        if test_file:
            os.unlink(test_file)


def test_error_handling():
    """Test error handling for invalid files."""
    print("\n🧪 Testing Error Handling")
    print("-" * 25)
    
    # Test non-existent file
    try:
        importer = AcademicCalendarImporter()
        importer.import_from_file("non_existent_file.ics")
        assert False, "Should have raised FileNotFoundError"
    except FileNotFoundError:
        print("✅ Non-existent file error handled correctly")
    
    # Test unsupported format
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Invalid content")
            test_file = f.name
        
        importer = AcademicCalendarImporter()
        importer.import_from_file(test_file)
        assert False, "Should have raised ValueError"
    except ValueError as e:
        print("✅ Unsupported format error handled correctly")
        os.unlink(test_file)
    
    # Test malformed CSV
    malformed_csv = """title,date,description
"Event 1","invalid-date","Description"
"Event 2","2025-08-15","Valid event"
"""
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(malformed_csv)
            test_file = f.name
        
        importer = AcademicCalendarImporter()
        imported_events, stats = importer.import_from_file(test_file)
        
        # Should import 1 valid event and skip 1 invalid (due to unparseable date)
        assert stats['successful_imports'] == 1, "Should import 1 valid event"
        assert stats['total_processed'] == 2, "Should process 2 rows"
        assert len(imported_events) == 1, "Should have 1 imported event"
        
        print("✅ Malformed data error handling test passed!")
        
    finally:
        os.unlink(test_file)


def run_all_tests():
    """Run all import functionality tests."""
    print("🧪 Calendar Import Functionality Tests")
    print("=" * 40)
    
    try:
        test_ical_import()
        test_csv_import()
        test_duplicate_detection()
        test_error_handling()
        
        print("\n🎉 All tests passed successfully!")
        print("✅ Calendar import functionality is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_tests()
