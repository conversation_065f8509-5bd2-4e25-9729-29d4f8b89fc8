#!/usr/bin/env python3
"""
Comprehensive Filter Validation Test
Validates all filtering functionality after the renaming refactoring.
"""

from academic_calendar_events import get_academic_calendar_events, AcademicCalendarEventType, AcademicCalendarEventCategory
from academic_calendar import AcademicCalendarApp

def test_filter_counts():
    """Test that all filter counts are correct and consistent."""
    print("🧪 Comprehensive Filter Validation Test")
    print("=" * 50)
    
    app = AcademicCalendarApp()
    all_events = get_academic_calendar_events()
    
    print(f"📊 Total Events: {len(all_events)}")
    expected_total = 50
    assert len(all_events) == expected_total, f"Expected {expected_total} total events, got {len(all_events)}"
    
    # Test Event Type Filters
    print("\n🔍 Event Type Filter Validation:")
    type_counts = {}
    for event_type in AcademicCalendarEventType:
        filtered = app.filter_events(event_type=event_type.value)
        type_counts[event_type.value] = len(filtered)
        print(f"  {event_type.value}: {len(filtered)} events")
    
    total_by_type = sum(type_counts.values())
    print(f"  Total by type: {total_by_type}")
    assert total_by_type == len(all_events), f"Type filter mismatch: {total_by_type} != {len(all_events)}"
    print("  ✅ Event type filters validated")
    
    # Test Term Filters
    print("\n🔍 Term Filter Validation:")
    terms = ["Fall 1 - 2025", "Fall 2 - 2025", "Spring 1 - 2026", "Spring 2 - 2026", "Summer 2026", "Fall 1 - 2026"]
    term_counts = {}
    for term in terms:
        filtered = app.filter_events(term=term)
        term_counts[term] = len(filtered)
        print(f"  {term}: {len(filtered)} events")
    
    total_by_term = sum(term_counts.values())
    print(f"  Total by term: {total_by_term}")
    assert total_by_term == len(all_events), f"Term filter mismatch: {total_by_term} != {len(all_events)}"
    print("  ✅ Term filters validated")
    
    # Test Category Filters
    print("\n🔍 Category Filter Validation:")
    category_counts = {}
    for category in AcademicCalendarEventCategory:
        filtered = app.filter_events(category=category.value)
        category_counts[category.value] = len(filtered)
        print(f"  {category.value}: {len(filtered)} events")
    
    total_by_category = sum(category_counts.values())
    print(f"  Total by category: {total_by_category}")
    assert total_by_category == len(all_events), f"Category filter mismatch: {total_by_category} != {len(all_events)}"
    print("  ✅ Category filters validated")
    
    # Test Combined Filters
    print("\n🔍 Combined Filter Validation:")
    
    # Fall 1 deadlines
    fall1_deadlines = app.filter_events(event_type="deadline", term="Fall 1 - 2025")
    print(f"  Fall 1 deadlines: {len(fall1_deadlines)} events")
    
    # Spring registration events
    spring_registration = app.filter_events(category="registration", term="Spring")
    print(f"  Spring registration events: {len(spring_registration)} events")
    
    # Holiday events
    holiday_events = app.filter_events(event_type="holiday", category="holiday")
    print(f"  Holiday events (type + category): {len(holiday_events)} events")
    
    print("  ✅ Combined filters working")
    
    # Test Partial Matching
    print("\n🔍 Partial Matching Validation:")
    
    fall_events = app.filter_events(term="Fall")
    expected_fall = term_counts["Fall 1 - 2025"] + term_counts["Fall 2 - 2025"] + term_counts["Fall 1 - 2026"]
    print(f"  'Fall' partial match: {len(fall_events)} events (expected: {expected_fall})")
    assert len(fall_events) == expected_fall, f"Fall partial match failed: {len(fall_events)} != {expected_fall}"
    
    spring_events = app.filter_events(term="Spring")
    expected_spring = term_counts["Spring 1 - 2026"] + term_counts["Spring 2 - 2026"]
    print(f"  'Spring' partial match: {len(spring_events)} events (expected: {expected_spring})")
    assert len(spring_events) == expected_spring, f"Spring partial match failed: {len(spring_events)} != {expected_spring}"
    
    year_2026_events = app.filter_events(term="2026")
    expected_2026 = term_counts["Spring 1 - 2026"] + term_counts["Spring 2 - 2026"] + term_counts["Summer 2026"] + term_counts["Fall 1 - 2026"]
    print(f"  '2026' partial match: {len(year_2026_events)} events (expected: {expected_2026})")
    assert len(year_2026_events) == expected_2026, f"2026 partial match failed: {len(year_2026_events)} != {expected_2026}"
    
    print("  ✅ Partial matching validated")
    
    # Summary
    print("\n📋 Filter Validation Summary:")
    print(f"  Total Events: {len(all_events)}")
    print(f"  Event Types: {len(AcademicCalendarEventType)} types, {total_by_type} total events")
    print(f"  Terms: {len(terms)} terms, {total_by_term} total events")
    print(f"  Categories: {len(AcademicCalendarEventCategory)} categories, {total_by_category} total events")
    print("\n🎉 All filter validations PASSED!")
    
    return True

if __name__ == "__main__":
    test_filter_counts()
