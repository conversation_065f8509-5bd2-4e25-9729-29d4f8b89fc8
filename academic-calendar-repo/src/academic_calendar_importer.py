#!/usr/bin/env python3
"""
Academic Calendar Import Module
Handles importing academic calendar events from external files (iCal, CSV, etc.)
"""

import os
import csv
import re
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path
import uuid

from academic_calendar_events import AcademicCalendarEvent, AcademicCalendarEventType, AcademicCalendarEventCategory


class AcademicCalendarImporter:
    """Handles importing calendar events from various file formats."""
    
    def __init__(self):
        self.supported_formats = ['.ics', '.csv']
        self.imported_events = []
        self.import_stats = {
            'total_processed': 0,
            'successful_imports': 0,
            'failed_imports': 0,
            'duplicates_skipped': 0,
            'errors': []
        }
    
    def import_from_file(self, file_path: str, existing_events: List[AcademicCalendarEvent] = None) -> Tuple[List[AcademicCalendarEvent], Dict[str, Any]]:
        """
        Import calendar events from a file.
        
        Args:
            file_path: Path to the calendar file
            existing_events: List of existing events to check for duplicates
            
        Returns:
            Tuple of (imported_events, import_statistics)
        """
        if existing_events is None:
            existing_events = []
        
        # Reset stats for new import
        self.imported_events = []
        self.import_stats = {
            'total_processed': 0,
            'successful_imports': 0,
            'failed_imports': 0,
            'duplicates_skipped': 0,
            'errors': []
        }
        
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if file_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_path.suffix}. Supported formats: {self.supported_formats}")
        
        try:
            if file_path.suffix.lower() == '.ics':
                self._import_ical(file_path, existing_events)
            elif file_path.suffix.lower() == '.csv':
                self._import_csv(file_path, existing_events)
            
            return self.imported_events, self.import_stats
            
        except Exception as e:
            self.import_stats['errors'].append(f"Import failed: {str(e)}")
            raise
    
    def _import_ical(self, file_path: Path, existing_events: List[AcademicCalendarEvent]):
        """Import events from iCal (.ics) file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
        
        # Parse iCal events
        events = self._parse_ical_content(content)
        
        for event_data in events:
            self.import_stats['total_processed'] += 1
            
            try:
                academic_event = self._convert_to_academic_event(event_data)
                
                if academic_event and not self._is_duplicate(academic_event, existing_events):
                    self.imported_events.append(academic_event)
                    self.import_stats['successful_imports'] += 1
                else:
                    self.import_stats['duplicates_skipped'] += 1
                    
            except Exception as e:
                self.import_stats['failed_imports'] += 1
                self.import_stats['errors'].append(f"Failed to import event: {str(e)}")
    
    def _import_csv(self, file_path: Path, existing_events: List[AcademicCalendarEvent]):
        """Import events from CSV file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # Try to detect delimiter
                sample = f.read(1024)
                f.seek(0)
                
                delimiter = ',' if sample.count(',') > sample.count(';') else ';'
                reader = csv.DictReader(f, delimiter=delimiter)
                
                for row in reader:
                    self.import_stats['total_processed'] += 1
                    
                    try:
                        academic_event = self._convert_csv_to_academic_event(row)
                        
                        if academic_event and not self._is_duplicate(academic_event, existing_events):
                            self.imported_events.append(academic_event)
                            self.import_stats['successful_imports'] += 1
                        else:
                            self.import_stats['duplicates_skipped'] += 1
                            
                    except Exception as e:
                        self.import_stats['failed_imports'] += 1
                        self.import_stats['errors'].append(f"Failed to import CSV row: {str(e)}")
                        
        except Exception as e:
            raise Exception(f"Error reading CSV file: {str(e)}")
    
    def _parse_ical_content(self, content: str) -> List[Dict[str, str]]:
        """Parse iCal content and extract event data."""
        events = []
        current_event = {}
        in_event = False
        
        lines = content.replace('\r\n', '\n').replace('\r', '\n').split('\n')
        
        for line in lines:
            line = line.strip()
            
            if line == 'BEGIN:VEVENT':
                in_event = True
                current_event = {}
            elif line == 'END:VEVENT':
                if in_event and current_event:
                    events.append(current_event.copy())
                in_event = False
                current_event = {}
            elif in_event and ':' in line:
                # Handle line continuations and property parameters
                if line.startswith(' ') or line.startswith('\t'):
                    # This is a continuation of the previous line
                    continue
                
                key, value = line.split(':', 1)
                
                # Handle property parameters (e.g., DTSTART;VALUE=DATE:20250801)
                if ';' in key:
                    key = key.split(';')[0]
                
                current_event[key] = value
        
        return events
    
    def _convert_to_academic_event(self, event_data: Dict[str, str]) -> Optional[AcademicCalendarEvent]:
        """Convert parsed iCal event data to AcademicCalendarEvent."""
        try:
            # Extract required fields
            title = event_data.get('SUMMARY', '').strip()
            if not title:
                return None
            
            # Parse date
            date_str = event_data.get('DTSTART', '')
            if not date_str:
                return None
            
            # Handle different date formats
            event_date = self._parse_date(date_str)
            if not event_date:
                return None
            
            # Extract description
            description = event_data.get('DESCRIPTION', '').replace('\\n', '\n').replace('\\,', ',')
            
            # Determine event type and category based on title and description
            event_type, category = self._categorize_event(title, description)
            
            # Determine term based on date
            term = self._determine_term(event_date)
            
            return AcademicCalendarEvent(
                title=title,
                date=event_date,
                event_type=event_type,
                category=category,
                description=description,
                term=term
            )
            
        except Exception as e:
            raise Exception(f"Error converting event '{event_data.get('SUMMARY', 'Unknown')}': {str(e)}")
    
    def _convert_csv_to_academic_event(self, row: Dict[str, str]) -> Optional[AcademicCalendarEvent]:
        """Convert CSV row to AcademicCalendarEvent."""
        try:
            # Try common CSV column names
            title_fields = ['title', 'summary', 'event', 'name', 'subject']
            date_fields = ['date', 'start_date', 'start', 'dtstart']
            desc_fields = ['description', 'desc', 'details', 'notes']
            
            title = None
            for field in title_fields:
                if field in row or field.lower() in row or field.upper() in row:
                    title = row.get(field) or row.get(field.lower()) or row.get(field.upper())
                    break
            
            if not title or not title.strip():
                return None
            
            # Find date field
            date_str = None
            for field in date_fields:
                if field in row or field.lower() in row or field.upper() in row:
                    date_str = row.get(field) or row.get(field.lower()) or row.get(field.upper())
                    break
            
            if not date_str:
                return None
            
            event_date = self._parse_date(date_str)
            if not event_date:
                return None
            
            # Find description
            description = ""
            for field in desc_fields:
                if field in row or field.lower() in row or field.upper() in row:
                    description = row.get(field) or row.get(field.lower()) or row.get(field.upper()) or ""
                    break
            
            # Categorize event
            event_type, category = self._categorize_event(title, description)
            
            # Determine term
            term = self._determine_term(event_date)
            
            return AcademicCalendarEvent(
                title=title.strip(),
                date=event_date,
                event_type=event_type,
                category=category,
                description=description.strip(),
                term=term
            )
            
        except Exception as e:
            raise Exception(f"Error converting CSV row: {str(e)}")
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """Parse various date formats."""
        date_str = date_str.strip()
        
        # Common date formats to try
        formats = [
            '%Y%m%d',           # 20250801
            '%Y-%m-%d',         # 2025-08-01
            '%m/%d/%Y',         # 08/01/2025
            '%d/%m/%Y',         # 01/08/2025
            '%Y/%m/%d',         # 2025/08/01
            '%B %d, %Y',        # August 01, 2025
            '%b %d, %Y',        # Aug 01, 2025
            '%d %B %Y',         # 01 August 2025
            '%d %b %Y',         # 01 Aug 2025
            '%Y%m%dT%H%M%S',    # 20250801T120000
            '%Y-%m-%dT%H:%M:%S' # 2025-08-01T12:00:00
        ]
        
        for fmt in formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt).date()
                return parsed_date
            except ValueError:
                continue
        
        return None

    def _categorize_event(self, title: str, description: str) -> Tuple[AcademicCalendarEventType, AcademicCalendarEventCategory]:
        """Categorize event based on title and description."""
        title_lower = title.lower()
        desc_lower = description.lower()
        combined = f"{title_lower} {desc_lower}"

        # Determine event type and category based on keywords
        if any(keyword in combined for keyword in ['deadline', 'last day', 'due', 'final day']):
            if any(keyword in combined for keyword in ['petition', 'graduate']):
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.PETITION
            elif any(keyword in combined for keyword in ['apply', 'application']):
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.APPLICATION
            elif any(keyword in combined for keyword in ['register', 'registration']):
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.REGISTRATION
            elif any(keyword in combined for keyword in ['payment', 'pay', 'tuition', 'fee']):
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.PAYMENT
            elif any(keyword in combined for keyword in ['withdraw', 'drop']):
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.WITHDRAWAL
            else:
                return AcademicCalendarEventType.DEADLINE, AcademicCalendarEventCategory.APPLICATION

        elif any(keyword in combined for keyword in ['holiday', 'closed', 'break']):
            return AcademicCalendarEventType.HOLIDAY, AcademicCalendarEventCategory.HOLIDAY

        elif any(keyword in combined for keyword in ['classes begin', 'start', 'first day']):
            return AcademicCalendarEventType.CLASS_DATE, AcademicCalendarEventCategory.CLASS_START

        elif any(keyword in combined for keyword in ['classes end', 'last day of classes', 'final day']):
            return AcademicCalendarEventType.CLASS_DATE, AcademicCalendarEventCategory.CLASS_END

        elif any(keyword in combined for keyword in ['commencement', 'graduation']):
            return AcademicCalendarEventType.CLASS_DATE, AcademicCalendarEventCategory.COMMENCEMENT

        elif any(keyword in combined for keyword in ['register', 'registration opens', 'first day to register']):
            return AcademicCalendarEventType.REGISTRATION, AcademicCalendarEventCategory.REGISTRATION

        else:
            # Default categorization
            return AcademicCalendarEventType.CLASS_DATE, AcademicCalendarEventCategory.CLASS_START

    def _determine_term(self, event_date: date) -> str:
        """Determine academic term based on event date."""
        year = event_date.year
        month = event_date.month

        # Academic year logic
        if month >= 8 and month <= 10:
            if month == 8 or (month == 9 and event_date.day <= 15):
                return f"Fall 1 - {year}"
            else:
                return f"Fall 2 - {year}"
        elif month >= 11 or month <= 3:
            if month >= 11:
                return f"Spring 1 - {year + 1}"
            else:
                if month <= 2:
                    return f"Spring 1 - {year}"
                else:
                    return f"Spring 2 - {year}"
        elif month >= 4 and month <= 7:
            return f"Summer {year}"
        else:
            return f"Academic Year {year}"

    def _is_duplicate(self, new_event: AcademicCalendarEvent, existing_events: List[AcademicCalendarEvent]) -> bool:
        """Check if event is a duplicate of existing events."""
        for existing in existing_events + self.imported_events:
            if (existing.title.lower().strip() == new_event.title.lower().strip() and
                existing.date == new_event.date):
                return True
        return False

    def get_import_summary(self) -> str:
        """Generate a summary of the import operation."""
        summary = []
        summary.append("📊 Import Summary")
        summary.append("=" * 20)
        summary.append(f"Total events processed: {self.import_stats['total_processed']}")
        summary.append(f"✅ Successfully imported: {self.import_stats['successful_imports']}")
        summary.append(f"⏭️  Duplicates skipped: {self.import_stats['duplicates_skipped']}")
        summary.append(f"❌ Failed imports: {self.import_stats['failed_imports']}")

        if self.import_stats['errors']:
            summary.append("\n🚨 Errors encountered:")
            for error in self.import_stats['errors'][:5]:  # Show first 5 errors
                summary.append(f"  - {error}")
            if len(self.import_stats['errors']) > 5:
                summary.append(f"  ... and {len(self.import_stats['errors']) - 5} more errors")

        return "\n".join(summary)


def browse_for_academic_calendar_file() -> Optional[str]:
    """
    Browse for an academic calendar file using GUI or command line.
    Returns the selected file path or None if cancelled.
    """
    try:
        # Try to use tkinter file dialog
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()  # Hide the main window

        file_path = filedialog.askopenfilename(
            title="Select Calendar File to Import",
            filetypes=[
                ("Calendar Files", "*.ics *.csv"),
                ("iCal Files", "*.ics"),
                ("CSV Files", "*.csv"),
                ("All Files", "*.*")
            ]
        )

        root.destroy()
        return file_path if file_path else None

    except ImportError:
        # Fallback to command line input
        print("📁 File Browser")
        print("Enter the path to your calendar file:")
        print("Supported formats: .ics, .csv")

        while True:
            file_path = input("File path (or 'cancel' to abort): ").strip()

            if file_path.lower() == 'cancel':
                return None

            if not file_path:
                print("Please enter a file path.")
                continue

            path = Path(file_path)
            if not path.exists():
                print(f"File not found: {file_path}")
                continue

            if path.suffix.lower() not in ['.ics', '.csv']:
                print(f"Unsupported file format: {path.suffix}")
                print("Supported formats: .ics, .csv")
                continue

            return str(path)


def interactive_academic_calendar_import() -> Optional[Tuple[List[AcademicCalendarEvent], Dict[str, Any]]]:
    """
    Interactive academic calendar import with user prompts.
    Returns tuple of (imported_events, import_stats) or None if cancelled.
    """
    print("📥 Academic Calendar Import Wizard")
    print("=" * 35)

    # Get file path
    print("\n1. Select academic calendar file to import")
    file_path = browse_for_academic_calendar_file()

    if not file_path:
        print("❌ Import cancelled.")
        return None

    print(f"📄 Selected file: {file_path}")

    # Load existing events
    from academic_calendar_events import get_academic_calendar_events
    existing_events = get_academic_calendar_events()

    # Import events
    print("\n2. Importing events...")
    importer = AcademicCalendarImporter()

    try:
        imported_events, import_stats = importer.import_from_file(file_path, existing_events)

        print("\n" + importer.get_import_summary())

        if imported_events:
            print(f"\n📅 Preview of imported events:")
            for i, event in enumerate(imported_events[:5], 1):
                print(f"  {i}. {event.title} - {event.date} ({event.term})")

            if len(imported_events) > 5:
                print(f"  ... and {len(imported_events) - 5} more events")

        return imported_events, import_stats

    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        return None


# Backward compatibility functions
def browse_for_file() -> Optional[str]:
    """Backward compatibility function."""
    return browse_for_academic_calendar_file()


def interactive_import() -> Optional[Tuple[List[AcademicCalendarEvent], Dict[str, Any]]]:
    """Backward compatibility function."""
    return interactive_academic_calendar_import()


class CalendarImporter(AcademicCalendarImporter):
    """Backward compatibility class."""
    pass


if __name__ == "__main__":
    # Test the import functionality
    result = interactive_academic_calendar_import()
    if result:
        imported_events, stats = result
        print(f"\n✅ Import completed successfully!")
        print(f"Imported {len(imported_events)} events.")
