#!/usr/bin/env python3
"""
Simple OAuth test script to verify Academic Calendar Google Calendar authentication.
This helps debug OAuth issues before running the full setup.
"""

import os
import sys
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

SCOPES = ['https://www.googleapis.com/auth/calendar']

def test_academic_calendar_oauth():
    """Test OAuth authentication with Academic Calendar Google Calendar."""
    print("🔐 Testing Academic Calendar Google Calendar OAuth Authentication")
    print("=" * 65)
    
    creds = None
    
    # Check for existing token
    if os.path.exists('token.json'):
        print("📄 Found existing token.json, loading...")
        creds = Credentials.from_authorized_user_file('token.json', SCOPES)
    
    # If no valid credentials, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("🔄 Refreshing expired credentials...")
            try:
                creds.refresh(Request())
                print("✅ Credentials refreshed successfully!")
            except Exception as e:
                print(f"❌ Failed to refresh credentials: {e}")
                creds = None
        
        if not creds:
            print("🌐 Starting new OAuth flow...")
            print("\n⚠️  If you see 'Access blocked' error:")
            print("   1. Go to Google Cloud Console OAuth consent screen")
            print("   2. Add your email as a test user")
            print("   3. Wait 2-3 minutes and try again")
            print("   4. Look for 'Advanced' link if you see warnings")
            print("   5. Click 'Go to [App Name] (unsafe)'")
            
            try:
                flow = InstalledAppFlow.from_client_secrets_file('credentials.json', SCOPES)
                creds = flow.run_local_server(port=0)
                print("✅ OAuth authentication successful!")
                
                # Save credentials for next run
                with open('token.json', 'w') as token:
                    token.write(creds.to_json())
                print("💾 Credentials saved to token.json")
                
            except Exception as e:
                print(f"❌ OAuth authentication failed: {e}")
                print("\n🔧 Troubleshooting steps:")
                print("   1. Check that credentials.json exists and is valid")
                print("   2. Add your email as test user in OAuth consent screen")
                print("   3. Try using incognito/private browser window")
                print("   4. Wait a few minutes after making OAuth changes")
                return False
    
    # Test API access
    try:
        print("🔍 Testing Google Calendar API access...")
        service = build('calendar', 'v3', credentials=creds)
        
        # Get calendar list
        calendars_result = service.calendarList().list().execute()
        calendars = calendars_result.get('items', [])
        
        print(f"✅ Successfully connected to Google Calendar!")
        print(f"📅 Found {len(calendars)} calendars in your account:")
        
        for i, calendar in enumerate(calendars[:5], 1):  # Show first 5
            primary = " (Primary)" if calendar.get('primary') else ""
            print(f"   {i}. {calendar['summary']}{primary}")
        
        if len(calendars) > 5:
            print(f"   ... and {len(calendars) - 5} more")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to access Google Calendar API: {e}")
        return False

def main():
    """Main function."""
    if not os.path.exists('credentials.json'):
        print("❌ credentials.json file not found!")
        print("Make sure you have downloaded and placed the OAuth credentials file.")
        return False

    success = test_academic_calendar_oauth()

    if success:
        print("\n🎉 Academic Calendar OAuth test successful!")
        print("✅ You can now run the full setup: python academic_calendar_setup.py")
    else:
        print("\n❌ Academic Calendar OAuth test failed.")
        print("📖 See OAUTH_FIX_GUIDE.md for troubleshooting steps.")

    return success

# Backward compatibility function
def test_oauth():
    """Backward compatibility function."""
    return test_academic_calendar_oauth()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
