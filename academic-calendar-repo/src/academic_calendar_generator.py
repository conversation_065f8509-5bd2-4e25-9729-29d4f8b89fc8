"""
iCal (.ics) file generator for academic calendar events.
Creates calendar files that can be imported into any calendar application.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List
import uuid
from academic_calendar_events import AcademicCalendarEvent, get_academic_calendar_events


class AcademicCalendarGenerator:
    """Generates iCal (.ics) files from academic calendar events."""

    def __init__(self):
        self.timezone = "America/Los_Angeles"  # Adjust as needed

    def generate_ical_content(self, events: List[AcademicCalendarEvent]) -> str:
        """
        Generate iCal content from a list of academic events.
        """
        lines = [
            "BEGIN:VCALENDAR",
            "VERSION:2.0",
            "PRODID:-//Academic Calendar//Academic Events//EN",
            "CALSCALE:GREGORIAN",
            "METHOD:PUBLISH",
            f"X-WR-CALNAME:Academic Calendar 2025-2026",
            f"X-WR-CALDESC:University Academic Calendar Events for 2025-2026 Academic Year",
            f"X-WR-TIMEZONE:{self.timezone}",
        ]
        
        for event in events:
            lines.extend(self._create_event_lines(event))
        
        lines.append("END:VCALENDAR")
        return "\r\n".join(lines)
    
    def _create_event_lines(self, event: AcademicCalendarEvent) -> List[str]:
        """Create iCal lines for a single event."""
        uid = str(uuid.uuid4())
        created_time = datetime.now().strftime("%Y%m%dT%H%M%SZ")
        
        # Convert date to datetime for all-day event
        event_datetime = datetime.combine(event.date, datetime.min.time())
        date_str = event_datetime.strftime("%Y%m%d")
        
        lines = [
            "BEGIN:VEVENT",
            f"UID:{uid}",
            f"DTSTAMP:{created_time}",
            f"DTSTART;VALUE=DATE:{date_str}",
            f"SUMMARY:{event.title}",
            f"DESCRIPTION:{event.description}\\n\\nTerm: {event.term}\\nCategory: {event.category.value}",
            f"CATEGORIES:{event.event_type.value.upper()},{event.category.value.upper()}",
            "STATUS:CONFIRMED",
            "TRANSP:TRANSPARENT",  # All-day event, doesn't block time
        ]
        
        # Add reminders based on event type
        for reminder_days in event.reminder_days:
            lines.extend(self._create_alarm_lines(reminder_days))
        
        lines.append("END:VEVENT")
        return lines
    
    def _create_alarm_lines(self, days_before: int) -> List[str]:
        """Create alarm/reminder lines for an event."""
        return [
            "BEGIN:VALARM",
            "ACTION:DISPLAY",
            f"DESCRIPTION:Reminder: Academic deadline in {days_before} day(s)",
            f"TRIGGER:-P{days_before}D",  # Days before event
            "END:VALARM"
        ]
    
    def save_ical_file(self, events: List[AcademicCalendarEvent], filename: str) -> str:
        """
        Save events to an iCal file.
        Returns the full path of the saved file.
        """
        content = self.generate_ical_content(events)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filename
    
    def create_separate_files_by_term(self, events: List[AcademicCalendarEvent]) -> List[str]:
        """
        Create separate iCal files for each term.
        Returns list of created filenames.
        """
        terms = set(event.term for event in events)
        created_files = []
        
        for term in terms:
            term_events = [event for event in events if event.term == term]
            if term_events:
                # Clean term name for filename
                clean_term = term.replace(" ", "_").replace("–", "-").replace(" ", "")
                filename = f"academic_calendar_{clean_term}.ics"
                self.save_ical_file(term_events, filename)
                created_files.append(filename)
        
        return created_files
    
    def create_combined_file(self, events: List[AcademicCalendarEvent]) -> str:
        """
        Create a single iCal file with all events.
        Returns the filename.
        """
        filename = "academic_calendar_2025_2026_complete.ics"
        self.save_ical_file(events, filename)
        return filename


def generate_academic_calendar_files():
    """
    Main function to generate all academic calendar files.
    """
    generator = AcademicCalendarGenerator()
    events = get_academic_calendar_events()

    print("Generating academic calendar files...")
    print(f"Total events: {len(events)}")

    # Create combined file
    combined_file = generator.create_combined_file(events)
    print(f"✓ Created combined calendar: {combined_file}")

    # Create separate files by term
    term_files = generator.create_separate_files_by_term(events)
    for file in term_files:
        print(f"✓ Created term-specific calendar: {file}")

    # Print event summary
    print("\nEvent Summary:")
    from academic_calendar_events import AcademicCalendarEventType
    for event_type in AcademicCalendarEventType:
        type_events = [e for e in events if e.event_type == event_type]
        print(f"  {event_type.value.title()}: {len(type_events)} events")

    return [combined_file] + term_files


# Backward compatibility function
def generate_calendar_files():
    """Backward compatibility function."""
    return generate_academic_calendar_files()


if __name__ == "__main__":
    generate_academic_calendar_files()
