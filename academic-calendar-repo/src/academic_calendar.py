#!/usr/bin/env python3
"""
Academic Calendar Application
Main application for managing university academic calendar events.

Features:
- Parse academic dates and events
- Generate iCal (.ics) files for calendar import
- Display events in various formats
- Set appropriate reminders for different event types
"""

import argparse
from datetime import datetime, date
from typing import List, Optional
import sys

from academic_calendar_events import (
    AcademicCalendarEvent, AcademicCalendarEventType, AcademicCalendarEventCategory,
    get_academic_calendar_events, get_academic_calendar_events_by_type, get_academic_calendar_events_by_term
)
from academic_calendar_generator import AcademicCalendarGenerator, generate_academic_calendar_files
from academic_calendar_importer import AcademicCalendarImporter, interactive_academic_calendar_import, browse_for_academic_calendar_file


class AcademicCalendarApp:
    """Main application class for academic calendar management."""

    def __init__(self):
        self.events = get_academic_calendar_events()
        self.academic_calendar_generator = AcademicCalendarGenerator()
    
    def display_events(self, events: Optional[List[AcademicCalendarEvent]] = None,
                      format_type: str = "table") -> None:
        """Display events in specified format."""
        if events is None:
            events = self.events

        if not events:
            print("No events found.")
            return

        if format_type == "table":
            self._display_table(events)
        elif format_type == "list":
            self._display_list(events)
        elif format_type == "summary":
            self._display_summary(events)

    def _display_table(self, events: List[AcademicCalendarEvent]) -> None:
        """Display events in table format."""
        print(f"\n{'Date':<12} {'Event':<50} {'Type':<12} {'Term':<15}")
        print("-" * 95)
        
        sorted_events = sorted(events, key=lambda x: x.date)
        for event in sorted_events:
            date_str = event.date.strftime("%Y-%m-%d")
            event_type = event.event_type.value.title()
            print(f"{date_str:<12} {event.title[:48]:<50} {event_type:<12} {event.term:<15}")
    
    def _display_list(self, events: List[AcademicCalendarEvent]) -> None:
        """Display events in detailed list format."""
        sorted_events = sorted(events, key=lambda x: x.date)

        for i, event in enumerate(sorted_events, 1):
            print(f"\n{i}. {event.title}")
            print(f"   Date: {event.date.strftime('%A, %B %d, %Y')}")
            print(f"   Type: {event.event_type.value.title()}")
            print(f"   Category: {event.category.value.title()}")
            print(f"   Term: {event.term}")
            if event.description:
                print(f"   Description: {event.description}")
            if event.reminder_days:
                reminders = ", ".join(f"{d} day(s)" for d in event.reminder_days)
                print(f"   Reminders: {reminders} before")

    def _display_summary(self, events: List[AcademicCalendarEvent]) -> None:
        """Display summary statistics."""
        print(f"\nAcademic Calendar Summary")
        print("=" * 30)
        print(f"Total Events: {len(events)}")
        
        # Group by event type
        print("\nBy Event Type:")
        for event_type in AcademicCalendarEventType:
            type_events = [e for e in events if e.event_type == event_type]
            print(f"  {event_type.value.title()}: {len(type_events)}")

        # Group by term
        print("\nBy Term:")
        terms = set(event.term for event in events)
        for term in sorted(terms):
            term_events = [e for e in events if e.term == term]
            print(f"  {term}: {len(term_events)}")

        # Upcoming events (next 30 days)
        today = date.today()
        upcoming = [e for e in events if e.date >= today and
                   (e.date - today).days <= 30]
        print(f"\nUpcoming Events (next 30 days): {len(upcoming)}")

    def filter_events(self, event_type: Optional[str] = None,
                     term: Optional[str] = None,
                     category: Optional[str] = None) -> List[AcademicCalendarEvent]:
        """Filter events by various criteria."""
        filtered_events = self.events.copy()
        
        if event_type:
            try:
                event_type_enum = AcademicCalendarEventType(event_type.lower())
                filtered_events = [e for e in filtered_events
                                 if e.event_type == event_type_enum]
            except ValueError:
                print(f"Invalid event type: {event_type}")
                return []

        if term:
            filtered_events = [e for e in filtered_events
                             if term.lower() in e.term.lower()]

        if category:
            try:
                category_enum = AcademicCalendarEventCategory(category.lower())
                filtered_events = [e for e in filtered_events
                                 if e.category == category_enum]
            except ValueError:
                print(f"Invalid category: {category}")
                return []

        return filtered_events

    def generate_calendars(self) -> List[str]:
        """Generate all calendar files."""
        return generate_academic_calendar_files()

    def get_upcoming_deadlines(self, days: int = 7) -> List[AcademicCalendarEvent]:
        """Get deadlines within specified number of days."""
        today = date.today()
        deadlines = [e for e in self.events
                    if e.event_type == AcademicCalendarEventType.DEADLINE and
                    e.date >= today and
                    (e.date - today).days <= days]
        return sorted(deadlines, key=lambda x: x.date)

    def import_calendar_file(self, file_path: str = None) -> Optional[List[AcademicCalendarEvent]]:
        """
        Import calendar events from external file.

        Args:
            file_path: Path to calendar file. If None, will prompt user to browse.

        Returns:
            List of imported events or None if import failed.
        """
        if file_path is None:
            file_path = browse_for_academic_calendar_file()
            if not file_path:
                print("❌ Import cancelled.")
                return None

        importer = AcademicCalendarImporter()

        try:
            imported_events, import_stats = importer.import_from_file(file_path, self.events)

            print("\n" + importer.get_import_summary())

            if imported_events:
                print(f"\n📅 Preview of imported events:")
                for i, event in enumerate(imported_events[:5], 1):
                    print(f"  {i}. {event.title} - {event.date} ({event.term})")

                if len(imported_events) > 5:
                    print(f"  ... and {len(imported_events) - 5} more events")

                # Ask user if they want to add events to the current collection
                add_to_collection = input("\nAdd imported events to current calendar? (y/n): ").lower() == 'y'

                if add_to_collection:
                    self.events.extend(imported_events)
                    print(f"✅ Added {len(imported_events)} events to calendar collection.")

                    # Ask if they want to regenerate files
                    regenerate = input("Regenerate iCal files with imported events? (y/n): ").lower() == 'y'
                    if regenerate:
                        files = self.generate_calendars()
                        print(f"✅ Regenerated {len(files)} calendar files.")

                    # Ask if they want to sync to Google Calendar
                    sync_google = input("Sync imported events to Google Calendar? (y/n): ").lower() == 'y'
                    if sync_google:
                        self._sync_to_google_calendar(imported_events)

                return imported_events
            else:
                print("ℹ️  No new events to import.")
                return []

        except Exception as e:
            print(f"❌ Import failed: {str(e)}")
            return None

    def _sync_to_google_calendar(self, events: List[AcademicCalendarEvent]):
        """Sync events to Google Calendar."""
        try:
            from academic_calendar_google_integration import AcademicCalendarGoogleIntegration

            integration = AcademicCalendarGoogleIntegration()
            results = integration.add_all_events(events, 'primary')

            print(f"✅ Synced {results['successful']} events to Google Calendar")
            if results['failed'] > 0:
                print(f"❌ Failed to sync {results['failed']} events")

        except Exception as e:
            print(f"❌ Google Calendar sync failed: {str(e)}")
            print("You can manually run the Google Calendar setup later.")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Academic Calendar Management Application"
    )
    parser.add_argument(
        "--action",
        choices=["display", "generate", "filter", "upcoming", "import"],
        default="display",
        help="Action to perform"
    )
    parser.add_argument(
        "--format", 
        choices=["table", "list", "summary"],
        default="table",
        help="Display format"
    )
    parser.add_argument(
        "--type", 
        choices=["deadline", "class_date", "holiday", "registration"],
        help="Filter by event type"
    )
    parser.add_argument(
        "--term", 
        help="Filter by term (partial match)"
    )
    parser.add_argument(
        "--category",
        choices=["petition", "application", "registration", "payment",
                "class_start", "withdrawal", "holiday", "class_end", "commencement"],
        help="Filter by category"
    )
    parser.add_argument(
        "--days",
        type=int,
        default=7,
        help="Number of days for upcoming deadlines (default: 7)"
    )
    parser.add_argument(
        "--import-file",
        dest="import_file",
        help="Path to calendar file to import (.ics or .csv)"
    )
    
    args = parser.parse_args()
    
    app = AcademicCalendarApp()
    
    if args.action == "display":
        if args.type or args.term or args.category:
            events = app.filter_events(args.type, args.term, args.category)
            print(f"Filtered Events ({len(events)} found):")
        else:
            events = None
            print("All Academic Calendar Events:")
        
        app.display_events(events, args.format)
    
    elif args.action == "generate":
        print("Generating calendar files...")
        files = app.generate_calendars()
        print(f"\nGenerated {len(files)} calendar files:")
        for file in files:
            print(f"  - {file}")
        print("\nYou can import these .ics files into any calendar application.")
    
    elif args.action == "filter":
        events = app.filter_events(args.type, args.term, args.category)
        print(f"Filtered Events ({len(events)} found):")
        app.display_events(events, args.format)
    
    elif args.action == "upcoming":
        deadlines = app.get_upcoming_deadlines(args.days)
        print(f"Upcoming Deadlines (next {args.days} days):")
        app.display_events(deadlines, args.format)

    elif args.action == "import":
        print("📥 Calendar Import")
        print("=" * 20)

        imported_events = app.import_calendar_file(args.import_file)

        if imported_events:
            print(f"\n🎉 Import completed successfully!")
            print(f"📊 Total events in calendar: {len(app.events)}")
        else:
            print("ℹ️  No events were imported.")


if __name__ == "__main__":
    main()
