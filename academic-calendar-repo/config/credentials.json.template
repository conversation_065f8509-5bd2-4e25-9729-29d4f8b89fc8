{
  "installed": ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}

<!-- 
Instructions for Google Calendar Integration:

1. Go to https://console.cloud.google.com/
2. Create a new project or select existing project
3. Enable Google Calendar API:
   - Go to "APIs & Services" → "Library"
   - Search for "Google Calendar API"
   - Click "Enable"
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "OAuth client ID"
   - Choose "Desktop application"
   - Download the JSON file
5. Rename the downloaded file to "credentials.json"
6. Place it in the root directory of the application
7. Run: python src/academic_calendar_setup.py

Note: Keep your credentials.json file secure and never commit it to version control.
-->
