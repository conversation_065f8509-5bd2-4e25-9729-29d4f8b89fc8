#!/bin/bash
# Academic Calendar Application Installation Script

echo "🎓 Academic Calendar Application - Installation Script"
echo "======================================================"

# Check Python version
echo "📋 Checking Python version..."
python_version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✅ Python $python_version is compatible (requires 3.8+)"
else
    echo "❌ Python $python_version is not compatible. Please install Python 3.8 or higher."
    exit 1
fi

# Create virtual environment
echo "🔧 Creating virtual environment..."
python3 -m venv calendar_env

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source calendar_env/bin/activate

# Upgrade pip
echo "🔧 Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Run initial test
echo "🧪 Running initial system test..."
python src/academic_calendar.py --format summary

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Installation completed successfully!"
    echo ""
    echo "📚 Quick Start:"
    echo "  1. Activate environment: source calendar_env/bin/activate"
    echo "  2. View all events: python src/academic_calendar.py"
    echo "  3. Generate calendars: python src/academic_calendar.py --action generate"
    echo "  4. View documentation: open docs/ACADEMIC_CALENDAR_USER_MANUAL.md"
    echo ""
    echo "🌐 For Google Calendar integration:"
    echo "  1. Get credentials from https://console.cloud.google.com/"
    echo "  2. Copy config/credentials.json.template to credentials.json"
    echo "  3. Run: python src/academic_calendar_setup.py"
    echo ""
else
    echo "❌ Installation test failed. Please check the error messages above."
    exit 1
fi
