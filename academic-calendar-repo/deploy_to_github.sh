#!/bin/bash
# Academic Calendar Application - GitHub Deployment Script
# Run this script after creating the GitHub repository at https://github.com/dainjaruss/academic-calendar

echo "🚀 Academic Calendar Application - GitHub Deployment"
echo "====================================================="

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "src" ] || [ ! -d "docs" ]; then
    echo "❌ Error: Please run this script from the academic-calendar-repo directory"
    echo "Current directory: $(pwd)"
    echo "Expected files: README.md, src/, docs/"
    exit 1
fi

echo "📋 Pre-deployment verification..."

# Check git status
echo "🔍 Checking git repository status..."
if [ ! -d ".git" ]; then
    echo "❌ Error: Not a git repository. Please run 'git init' first."
    exit 1
fi

# Check if remote origin is set
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "❌ Error: No remote origin set. Please run:"
    echo "   git remote add origin https://github.com/dainjaruss/academic-calendar.git"
    exit 1
fi

echo "✅ Git repository configured correctly"

# Verify core functionality
echo "🧪 Testing core application functionality..."
if python src/academic_calendar.py --format summary > /dev/null 2>&1; then
    echo "✅ Core application working correctly"
else
    echo "❌ Error: Core application test failed"
    exit 1
fi

# Check file counts
python_files=$(find . -name "*.py" | wc -l | tr -d ' ')
md_files=$(find . -name "*.md" | wc -l | tr -d ' ')
ics_files=$(find . -name "*.ics" | wc -l | tr -d ' ')

echo "📊 Repository contents:"
echo "   Python files: $python_files"
echo "   Documentation files: $md_files"
echo "   Calendar files: $ics_files"

if [ "$python_files" -lt 10 ] || [ "$md_files" -lt 8 ] || [ "$ics_files" -lt 7 ]; then
    echo "❌ Error: Missing files. Expected at least 10 Python, 8 Markdown, 7 Calendar files"
    exit 1
fi

echo "✅ All required files present"

# Check git status
echo "🔍 Checking git status..."
if [ -n "$(git status --porcelain)" ]; then
    echo "📝 Uncommitted changes found. Committing..."
    git add .
    git commit -m "Final pre-deployment commit - ensure all files are included"
fi

echo "✅ Repository ready for deployment"

# Push to GitHub
echo "🚀 Pushing to GitHub..."
echo "Repository: https://github.com/dainjaruss/academic-calendar"

if git push -u origin main; then
    echo ""
    echo "🎉 SUCCESS! Repository deployed to GitHub"
    echo ""
    echo "📊 Deployment Summary:"
    echo "   Repository: https://github.com/dainjaruss/academic-calendar"
    echo "   Total Files: $(find . -type f ! -path './.git/*' | wc -l | tr -d ' ')"
    echo "   Academic Events: 50"
    echo "   Personal Courses: 4"
    echo "   Documentation Files: $md_files"
    echo ""
    echo "🔗 Next Steps:"
    echo "   1. Visit: https://github.com/dainjaruss/academic-calendar"
    echo "   2. Add repository topics: python, calendar, academic-planning, ical, google-calendar"
    echo "   3. Enable Issues and other features"
    echo "   4. Verify README.md displays correctly"
    echo "   5. Test cloning and functionality"
    echo ""
    echo "📚 Documentation available at:"
    echo "   - User Manual: docs/ACADEMIC_CALENDAR_USER_MANUAL.md"
    echo "   - Quick Reference: docs/ACADEMIC_CALENDAR_QUICK_REFERENCE.md"
    echo "   - Technical Docs: docs/ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md"
    echo ""
else
    echo "❌ Error: Failed to push to GitHub"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "   1. Ensure the repository exists: https://github.com/dainjaruss/academic-calendar"
    echo "   2. Check your GitHub authentication"
    echo "   3. Verify repository permissions"
    echo ""
    echo "📝 Manual deployment steps:"
    echo "   1. Create repository at: https://github.com/new"
    echo "   2. Repository name: academic-calendar"
    echo "   3. Don't initialize with README (we have one)"
    echo "   4. Run: git push -u origin main"
    exit 1
fi
