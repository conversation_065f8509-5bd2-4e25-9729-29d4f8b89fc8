# Academic Calendar Application Dependencies

# Core dependencies (always required)
python-dateutil>=2.8.0

# Google Calendar Integration (optional)
google-auth>=2.0.0
google-auth-oauthlib>=0.5.0
google-api-python-client>=2.0.0

# Development and testing (optional)
pytest>=6.0.0
pytest-cov>=2.10.0

# Documentation generation (optional)
markdown>=3.3.0

# Note: This application is designed to work with Python 3.8+
# Most functionality works with just the Python standard library
# Google Calendar integration requires the google-* packages above
