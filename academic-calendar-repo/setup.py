#!/usr/bin/env python3
"""
Academic Calendar Application Setup
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements from requirements.txt
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="academic-calendar",
    version="1.0.0",
    author="Academic Calendar Team",
    author_email="<EMAIL>",
    description="Comprehensive Academic Calendar Application for university academic planning",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/academic-calendar",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Education",
        "Topic :: Education",
        "Topic :: Office/Business :: Scheduling",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=[
        "python-dateutil>=2.8.0",
    ],
    extras_require={
        "google": [
            "google-auth>=2.0.0",
            "google-auth-oauthlib>=0.5.0",
            "google-api-python-client>=2.0.0",
        ],
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "academic-calendar=academic_calendar:main",
            "academic-calendar-setup=academic_calendar_setup:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.csv", "*.json.template"],
    },
    keywords="academic calendar university planning ical google-calendar education",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/academic-calendar/issues",
        "Source": "https://github.com/yourusername/academic-calendar",
        "Documentation": "https://github.com/yourusername/academic-calendar/tree/main/docs",
    },
)
