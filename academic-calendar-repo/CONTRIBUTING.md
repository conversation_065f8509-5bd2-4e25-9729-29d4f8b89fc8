# Contributing to Academic Calendar Application

Thank you for your interest in contributing to the Academic Calendar Application! This document provides guidelines and information for contributors.

## 🎯 **How to Contribute**

### **Types of Contributions**
- 🐛 **Bug Reports**: Report issues or unexpected behavior
- ✨ **Feature Requests**: Suggest new features or improvements
- 📝 **Documentation**: Improve or add documentation
- 🔧 **Code Contributions**: Fix bugs or implement features
- 🧪 **Testing**: Add or improve tests
- 🌐 **Translations**: Add support for other languages/regions

## 🚀 **Getting Started**

### **Development Setup**
1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/yourusername/academic-calendar.git
   cd academic-calendar
   ```
3. **Set up development environment**:
   ```bash
   python -m venv calendar_env
   source calendar_env/bin/activate  # On Windows: calendar_env\Scripts\activate
   pip install -r requirements.txt
   ```
4. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

### **Development Guidelines**

#### **Code Style**
- Follow PEP 8 Python style guidelines
- Use type hints for function parameters and return values
- Write descriptive docstrings for all classes and functions
- Use meaningful variable and function names

#### **Testing**
- Write tests for new features
- Ensure all existing tests pass
- Run the test suite:
  ```bash
  python src/academic_calendar_test.py
  python src/academic_calendar_test_import.py
  python src/filter_validation_test.py
  ```

#### **Documentation**
- Update documentation for new features
- Follow the existing documentation style
- Include examples in docstrings
- Update the README if necessary

## 📝 **Contribution Process**

### **1. Issue Creation**
- Check existing issues before creating new ones
- Use clear, descriptive titles
- Provide detailed descriptions with steps to reproduce (for bugs)
- Include system information (Python version, OS, etc.)

### **2. Pull Request Process**
1. **Ensure your code follows the guidelines above**
2. **Update documentation** as needed
3. **Add or update tests** for your changes
4. **Ensure all tests pass**
5. **Create a pull request** with:
   - Clear title describing the change
   - Detailed description of what was changed and why
   - Reference to related issues (if any)

### **3. Code Review**
- Be responsive to feedback
- Make requested changes promptly
- Discuss any disagreements respectfully
- Ensure CI checks pass

## 🐛 **Bug Reports**

### **Before Reporting**
- Check if the issue already exists
- Try the latest version
- Review the documentation and FAQ

### **Bug Report Template**
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Run command '...'
2. See error

**Expected behavior**
What you expected to happen.

**System Information**
- OS: [e.g., macOS 12.0, Windows 10, Ubuntu 20.04]
- Python version: [e.g., 3.9.7]
- Academic Calendar version: [e.g., 1.0.0]

**Additional context**
Any other context about the problem.
```

## ✨ **Feature Requests**

### **Feature Request Template**
```markdown
**Is your feature request related to a problem?**
A clear description of what the problem is.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
Other solutions or features you've considered.

**Additional context**
Any other context or screenshots about the feature request.
```

## 🔧 **Development Areas**

### **High Priority**
- Additional academic calendar data for other universities
- Support for different academic year formats
- Enhanced Google Calendar integration features
- Mobile-friendly calendar export options

### **Medium Priority**
- Integration with other calendar services (Outlook 365, etc.)
- Advanced filtering and search capabilities
- Automated academic year transitions
- Performance optimizations

### **Low Priority**
- GUI interface
- Web-based version
- Additional export formats
- Internationalization

## 📚 **Resources**

### **Documentation**
- [User Manual](docs/ACADEMIC_CALENDAR_USER_MANUAL.md)
- [Technical Documentation](docs/ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md)
- [API Reference](docs/ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md#api-reference)

### **Development Tools**
- **Testing**: pytest for unit tests
- **Code Quality**: Follow PEP 8 standards
- **Documentation**: Markdown for documentation
- **Version Control**: Git with descriptive commit messages

## 🏆 **Recognition**

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- Special thanks in documentation

## 📞 **Getting Help**

- **Documentation**: Check the comprehensive docs first
- **Issues**: Search existing GitHub issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: Contact maintainers for sensitive issues

## 📄 **License**

By contributing, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for helping make the Academic Calendar Application better for everyone!** 🎓
