# 🎓 Academic Calendar Application

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Calendar Format: iCal](https://img.shields.io/badge/format-iCal-green.svg)](https://tools.ietf.org/html/rfc5545)

A comprehensive Python application for managing university academic calendar events with personal course tracking, calendar integration, and multi-platform export capabilities.

## ✨ **Features**

- 📅 **50+ Academic Events** across 6 terms (Fall 2025 - Fall 2026)
- 📚 **Personal Course Tracking** with status management
- 🔍 **Flexible Filtering** by term, type, and category
- 📤 **Universal Calendar Export** (iCal format for Google, Apple, Outlook)
- 🌐 **Google Calendar Integration** with automatic synchronization
- 📥 **Import Functionality** for CSV and iCal files
- 🎯 **Smart Reminders** based on event types
- 📊 **Integrated Academic View** combining calendar and courses

## 🚀 **Quick Start**

### **Installation**

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/academic-calendar.git
   cd academic-calendar
   ```

2. **Set up virtual environment:**
   ```bash
   python -m venv calendar_env
   source calendar_env/bin/activate  # On Windows: calendar_env\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

### **Basic Usage**

```bash
# View all academic events
python src/academic_calendar.py

# Generate calendar files
python src/academic_calendar.py --action generate

# View your course schedule
python src/personal_course_schedule.py

# Comprehensive academic overview
python src/integrated_academic_view.py
```

### **Quick Commands**

| Task | Command |
|------|---------|
| **View all events** | `python src/academic_calendar.py` |
| **Show upcoming deadlines** | `python src/academic_calendar.py --action upcoming` |
| **Filter by term** | `python src/academic_calendar.py --term "Fall 1 - 2025"` |
| **Generate calendars** | `python src/academic_calendar.py --action generate` |
| **Import events** | `python src/academic_calendar.py --action import --import-file file.csv` |

## 📊 **System Overview**

### **Academic Calendar Data**
- **Total Events**: 50 academic calendar events
- **Terms Covered**: 6 terms (Fall 2025 through Fall 2026)
- **Event Types**: Deadlines, Class Dates, Holidays, Registration
- **Categories**: 9 categories including petitions, applications, payments, etc.

### **Personal Course Tracking**
- **Sample Courses**: 4 CIS courses across multiple terms
- **Status Tracking**: Registered, Planned, Completed, Dropped
- **Credit Management**: Automatic credit hour calculation
- **Term Integration**: Links courses with relevant academic events

## 📁 **Project Structure**

```
academic-calendar/
├── src/                           # Core application files
│   ├── academic_calendar.py      # Main application
│   ├── academic_calendar_events.py # Event data (50 events)
│   ├── personal_course_schedule.py # Course tracking
│   └── integrated_academic_view.py # Unified view
├── docs/                          # Complete documentation
│   ├── ACADEMIC_CALENDAR_USER_MANUAL.md
│   ├── ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md
│   ├── ACADEMIC_CALENDAR_QUICK_REFERENCE.md
│   └── ACADEMIC_CALENDAR_INTEGRATION_GUIDE.md
├── tests/                         # Test files
├── samples/                       # Sample import files
├── output/                        # Generated calendar files
├── config/                        # Configuration templates
└── requirements.txt               # Dependencies
```

## 🔧 **Advanced Features**

### **Google Calendar Integration**
1. Get credentials from [Google Cloud Console](https://console.cloud.google.com/)
2. Copy `config/credentials.json.template` to `credentials.json`
3. Run setup: `python src/academic_calendar_setup.py`

### **Custom Event Import**
```bash
# Import from CSV
python src/academic_calendar.py --action import --import-file samples/custom_events.csv

# Interactive import wizard
python src/academic_calendar_importer.py
```

### **Filtering Examples**
```bash
# Fall 1 deadlines only
python src/academic_calendar.py --term "Fall 1" --type deadline

# All registration events
python src/academic_calendar.py --category registration

# Upcoming deadlines (next 30 days)
python src/academic_calendar.py --action upcoming --days 30
```

## 📚 **Documentation**

Comprehensive documentation is available in the `docs/` directory:

- **[User Manual](docs/ACADEMIC_CALENDAR_USER_MANUAL.md)** - Complete usage guide
- **[Quick Reference](docs/ACADEMIC_CALENDAR_QUICK_REFERENCE.md)** - Command cheat sheet
- **[Technical Documentation](docs/ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md)** - Developer reference
- **[Integration Guide](docs/ACADEMIC_CALENDAR_INTEGRATION_GUIDE.md)** - Advanced workflows
- **[FAQ](docs/ACADEMIC_CALENDAR_FAQ.md)** - Common questions and troubleshooting

## 🧪 **Testing**

```bash
# Run core functionality tests
python src/academic_calendar_test.py

# Test import functionality
python src/academic_calendar_test_import.py

# Validate all filters
python src/filter_validation_test.py
```

## 📤 **Calendar Export**

The application generates standard iCal (.ics) files compatible with:
- ✅ Google Calendar
- ✅ Apple Calendar
- ✅ Microsoft Outlook
- ✅ Thunderbird
- ✅ Any RFC 5545 compliant calendar

## 🎯 **Use Cases**

- **Students**: Track academic deadlines and course schedules
- **Academic Staff**: Manage institutional calendar events
- **Administrators**: Distribute academic calendar information
- **Developers**: Extend with custom academic planning features

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: Check the comprehensive docs in the `docs/` directory
- **Issues**: Report bugs or request features via GitHub Issues
- **FAQ**: Common questions answered in [FAQ](docs/ACADEMIC_CALENDAR_FAQ.md)

## 🏆 **Acknowledgments**

- Built for academic planning and calendar management
- Supports 2025-2026 academic year with extensible design
- Integrates with major calendar platforms for universal compatibility

---

**Start managing your academic calendar efficiently today!** 🎓📅
