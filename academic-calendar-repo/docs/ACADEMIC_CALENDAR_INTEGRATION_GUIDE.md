# 🔗 Academic Calendar Application - Integration Guide

## 📋 **Table of Contents**
1. [Personal Course Schedule Integration](#personal-course-schedule-integration)
2. [Custom Event Creation and Management](#custom-event-creation-and-management)
3. [Calendar Synchronization with External Systems](#calendar-synchronization-with-external-systems)
4. [Backup and Data Management](#backup-and-data-management)
5. [Advanced Workflows](#advanced-workflows)
6. [API Integration Examples](#api-integration-examples)

---

## 📚 **Personal Course Schedule Integration**

### **Setting Up Your Course Schedule**

#### **Step 1: Configure Your Courses**
Edit `personal_course_schedule.py` to add your specific courses:

```python
def get_personal_course_schedule() -> List[PersonalCourse]:
    courses = [
        PersonalCourse(
            course_code="CIS 5600",
            course_title="Information Security Management",
            term="Fall 1 - 2025",
            status=CourseStatus.REGISTERED,
            credits=3,
            notes="Already registered for Fall 1 - 2025"
        ),
        # Add your additional courses here
        PersonalCourse(
            course_code="YOUR_COURSE_CODE",
            course_title="Your Course Title",
            term="Your Term",
            status=CourseStatus.PLANNED,
            credits=3,
            notes="Your notes"
        )
    ]
    return courses
```

#### **Step 2: Course Status Management**
Update course status as you progress:

```python
# Status options
CourseStatus.REGISTERED  # Currently enrolled
CourseStatus.PLANNED     # Future enrollment
CourseStatus.COMPLETED   # Successfully finished
CourseStatus.DROPPED     # Withdrawn/cancelled
```

#### **Step 3: Integrated View Usage**
```bash
# View comprehensive academic overview
python integrated_academic_view.py

# View specific term with your courses
python academic_calendar.py --term "Fall 1 - 2025" --format list
```

### **Course-Specific Event Filtering**

#### **Filter Events for Your Course Terms**
```bash
# Fall 1 - 2025 (CIS 5600)
python academic_calendar.py --term "Fall 1 - 2025" --format list

# Spring 2 - 2026 (CIS 5898)
python academic_calendar.py --term "Spring 2 - 2026" --format list

# All your course terms
python academic_calendar.py --term "Fall 1" --term "Fall 2" --term "Spring 1" --term "Spring 2"
```

#### **Deadline Tracking for Your Courses**
```bash
# Upcoming deadlines for your terms
python academic_calendar.py --action upcoming --days 30

# Registration deadlines for your planned courses
python academic_calendar.py --category registration --format list
```

### **Academic Progress Tracking**

#### **Credit Hour Calculation**
```python
def calculate_progress():
    courses = get_personal_course_schedule()
    completed_credits = sum(c.credits for c in courses if c.status == CourseStatus.COMPLETED)
    planned_credits = sum(c.credits for c in courses if c.status in [CourseStatus.REGISTERED, CourseStatus.PLANNED])
    total_credits = completed_credits + planned_credits
    
    print(f"Completed Credits: {completed_credits}")
    print(f"Planned Credits: {planned_credits}")
    print(f"Total Program Credits: {total_credits}")
```

#### **Term-by-Term Planning**
```bash
# View integrated term overview
python integrated_academic_view.py

# Focus on specific term planning
python academic_calendar.py --term "Spring 1 - 2026" --category registration
```

---

## ✨ **Custom Event Creation and Management**

### **Adding Custom Academic Events**

#### **Method 1: Direct Code Addition**
Add events directly to `academic_calendar_events.py`:

```python
# Add to the events list in get_academic_calendar_events()
AcademicCalendarEvent(
    title="Your Custom Event",
    date=date(2025, 12, 1),
    event_type=AcademicCalendarEventType.DEADLINE,
    category=AcademicCalendarEventCategory.APPLICATION,
    description="Your custom event description",
    term="Fall 2 - 2025"
),
```

#### **Method 2: CSV Import**
Create a CSV file with your custom events:

```csv
title,date,description,event_type,category,term
"Study Group Meeting","2025-09-15","Weekly study group for CIS 5600","class_date","class_start","Fall 1 - 2025"
"Project Deadline","2025-10-30","Final project submission","deadline","application","Fall 1 - 2025"
"Career Fair","2025-11-15","University career fair","class_date","class_start","Fall 2 - 2025"
```

Import the custom events:
```bash
python academic_calendar.py --action import --import-file custom_events.csv
```

### **Personal Event Categories**

#### **Creating Custom Categories**
Extend the category system for personal use:

```python
# Add to AcademicCalendarEventCategory enum
PERSONAL_STUDY = "personal_study"
PERSONAL_PROJECT = "personal_project"
PERSONAL_MEETING = "personal_meeting"
```

#### **Personal Event Templates**
```csv
title,date,description,event_type,category,term
"Study Session: CIS 5600","2025-09-01","Prepare for Information Security exam","deadline","personal_study","Fall 1 - 2025"
"Project Milestone","2025-10-15","Complete project phase 1","deadline","personal_project","Fall 1 - 2025"
"Advisor Meeting","2025-11-01","Academic advising appointment","class_date","personal_meeting","Fall 2 - 2025"
```

### **Event Modification Workflows**

#### **Updating Existing Events**
```python
# Create modified event list
def update_event_description(events, title_to_update, new_description):
    for event in events:
        if event.title == title_to_update:
            event.description = new_description
    return events
```

#### **Bulk Event Operations**
```python
# Add reminder days to all deadlines
def add_reminders_to_deadlines(events):
    for event in events:
        if event.event_type == AcademicCalendarEventType.DEADLINE:
            if not event.reminder_days:
                event.reminder_days = [7, 1]  # 7 days and 1 day before
    return events
```

---

## 🔄 **Calendar Synchronization with External Systems**

### **Google Calendar Integration**

#### **Advanced Google Calendar Setup**
```python
# Custom calendar creation
def create_academic_calendar():
    integration = AcademicCalendarGoogleIntegration()
    
    # Create separate calendars for different purposes
    academic_calendar = integration.create_calendar(
        "Academic Calendar 2025-2026",
        "Official university academic calendar events"
    )
    
    personal_calendar = integration.create_calendar(
        "Personal Academic Schedule",
        "Personal course schedule and study events"
    )
    
    return academic_calendar, personal_calendar
```

#### **Selective Event Synchronization**
```python
# Sync only specific event types
def sync_deadlines_only():
    integration = AcademicCalendarGoogleIntegration()
    events = get_academic_calendar_events()
    deadlines = [e for e in events if e.event_type == AcademicCalendarEventType.DEADLINE]
    
    results = integration.add_all_events(deadlines, 'primary')
    return results
```

#### **Automated Sync Scheduling**
```bash
# Create a cron job for daily sync (Linux/Mac)
# Add to crontab: crontab -e
0 9 * * * cd /path/to/academic-calendar && source calendar_env/bin/activate && python sync_google_calendar.py
```

### **Multi-Platform Calendar Sync**

#### **iCal File Distribution**
```python
# Generate and distribute calendar files
def distribute_calendars():
    generator = AcademicCalendarGenerator()
    events = get_academic_calendar_events()
    
    # Generate files for different purposes
    all_events_file = generator.create_combined_file(events)
    deadlines_file = generator.save_ical_file(
        [e for e in events if e.event_type == AcademicCalendarEventType.DEADLINE],
        "academic_deadlines_only.ics"
    )
    
    # Copy to shared locations
    import shutil
    shutil.copy(all_events_file, "/path/to/shared/drive/")
    shutil.copy(deadlines_file, "/path/to/shared/drive/")
```

#### **Cross-Platform Compatibility**
```python
# Ensure timezone compatibility
def set_timezone_for_platform(platform):
    timezones = {
        'apple': 'America/Los_Angeles',
        'google': 'America/Los_Angeles',
        'outlook': 'Pacific Standard Time',
        'thunderbird': 'America/Los_Angeles'
    }
    return timezones.get(platform, 'America/Los_Angeles')
```

### **External System Integration**

#### **University LMS Integration**
```python
# Example: Canvas LMS integration
def sync_with_canvas():
    # This would require Canvas API credentials
    canvas_events = fetch_canvas_assignments()  # Hypothetical function
    
    # Convert Canvas events to Academic Calendar format
    academic_events = []
    for canvas_event in canvas_events:
        academic_event = AcademicCalendarEvent(
            title=f"Canvas: {canvas_event['name']}",
            date=canvas_event['due_date'],
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description=canvas_event['description'],
            term=determine_term_from_date(canvas_event['due_date'])
        )
        academic_events.append(academic_event)
    
    return academic_events
```

#### **Email Integration**
```python
# Email reminders for upcoming deadlines
def send_deadline_reminders():
    import smtplib
    from email.mime.text import MIMEText
    
    app = AcademicCalendarApp()
    upcoming_deadlines = app.get_upcoming_deadlines(7)
    
    if upcoming_deadlines:
        message = "Upcoming Academic Deadlines:\n\n"
        for deadline in upcoming_deadlines:
            days_until = (deadline.date - date.today()).days
            message += f"• {deadline.title} - {deadline.date} ({days_until} days)\n"
        
        # Send email (requires email configuration)
        # send_email("Academic Deadline Reminder", message)
```

---

## 💾 **Backup and Data Management**

### **Data Backup Strategies**

#### **Automated Backup Script**
```python
#!/usr/bin/env python3
"""
Academic Calendar Backup Script
Creates backups of all academic calendar data and configurations.
"""

import os
import shutil
import datetime
from pathlib import Path

def create_backup():
    backup_dir = f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Backup core files
    files_to_backup = [
        'academic_calendar_events.py',
        'personal_course_schedule.py',
        'credentials.json',
        'token.json'
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
    
    # Backup generated calendar files
    for ics_file in Path('.').glob('*.ics'):
        shutil.copy2(ics_file, backup_dir)
    
    print(f"✅ Backup created: {backup_dir}")
    return backup_dir

if __name__ == "__main__":
    create_backup()
```

#### **Data Export and Import**
```python
# Export all data to JSON
def export_academic_data():
    import json
    from datetime import date
    
    events = get_academic_calendar_events()
    courses = get_personal_course_schedule()
    
    # Convert to serializable format
    export_data = {
        'events': [
            {
                'title': event.title,
                'date': event.date.isoformat(),
                'event_type': event.event_type.value,
                'category': event.category.value,
                'description': event.description,
                'term': event.term,
                'reminder_days': event.reminder_days
            }
            for event in events
        ],
        'courses': [
            {
                'course_code': course.course_code,
                'course_title': course.course_title,
                'term': course.term,
                'status': course.status.value,
                'credits': course.credits,
                'grade': course.grade,
                'notes': course.notes
            }
            for course in courses
        ],
        'export_date': datetime.datetime.now().isoformat()
    }
    
    with open('academic_calendar_export.json', 'w') as f:
        json.dump(export_data, f, indent=2)
    
    print("✅ Data exported to academic_calendar_export.json")
```

### **Version Control Integration**

#### **Git Repository Setup**
```bash
# Initialize git repository for academic calendar
git init
git add *.py *.md *.csv
git commit -m "Initial academic calendar setup"

# Create branches for different academic years
git branch academic-year-2025-2026
git branch academic-year-2026-2027

# Track changes to personal schedule
git add personal_course_schedule.py
git commit -m "Updated course schedule for Spring 2026"
```

#### **Configuration Management**
```python
# config.py - Centralized configuration
class AcademicCalendarConfig:
    TIMEZONE = "America/Los_Angeles"
    ACADEMIC_YEAR = "2025-2026"
    DEFAULT_REMINDER_DAYS = {
        'deadline': [7, 1],
        'class_date': [1],
        'registration': [7],
        'holiday': []
    }
    
    # Personal settings
    STUDENT_NAME = "Your Name"
    STUDENT_ID = "Your ID"
    PROGRAM = "Computer Information Systems"
    
    # File paths
    BACKUP_DIR = "backups"
    EXPORT_DIR = "exports"
    CALENDAR_OUTPUT_DIR = "calendars"
```

### **Data Migration and Updates**

#### **Academic Year Transition**
```python
def migrate_to_new_academic_year(old_year, new_year):
    """Migrate data from one academic year to another."""
    
    # Update term names
    def update_term_year(term):
        return term.replace(old_year, new_year)
    
    # Migrate course schedule
    courses = get_personal_course_schedule()
    for course in courses:
        if course.status == CourseStatus.PLANNED:
            course.term = update_term_year(course.term)
        elif course.status == CourseStatus.REGISTERED:
            course.status = CourseStatus.COMPLETED
    
    # Archive old events
    old_events = get_academic_calendar_events()
    archive_events(old_events, old_year)
    
    print(f"✅ Migrated from {old_year} to {new_year}")
```

#### **Data Validation and Cleanup**
```python
def validate_and_cleanup_data():
    """Validate data integrity and clean up inconsistencies."""
    
    events = get_academic_calendar_events()
    courses = get_personal_course_schedule()
    
    # Validate event data
    for event in events:
        assert event.date >= date.today() - timedelta(days=365), f"Event too old: {event.title}"
        assert event.event_type in AcademicCalendarEventType, f"Invalid event type: {event.event_type}"
        assert event.category in AcademicCalendarEventCategory, f"Invalid category: {event.category}"
    
    # Validate course data
    for course in courses:
        assert course.credits > 0, f"Invalid credits: {course.course_code}"
        assert course.status in CourseStatus, f"Invalid status: {course.status}"
    
    print("✅ Data validation completed")
```

---

## 🚀 **Advanced Workflows**

### **Automated Academic Planning**

#### **Smart Deadline Tracking**
```python
def create_smart_deadline_alerts():
    """Create intelligent deadline alerts based on course workload."""
    
    app = AcademicCalendarApp()
    courses = get_personal_course_schedule()
    
    # Get deadlines for your enrolled courses
    enrolled_terms = [c.term for c in courses if c.status == CourseStatus.REGISTERED]
    
    for term in enrolled_terms:
        term_deadlines = app.filter_events(term=term, event_type="deadline")
        
        # Create study schedule leading up to deadlines
        for deadline in term_deadlines:
            if deadline.category == AcademicCalendarEventCategory.REGISTRATION:
                # Create reminder 2 weeks before registration
                study_date = deadline.date - timedelta(days=14)
                print(f"📅 Plan to register by {study_date} for {deadline.title}")
```

#### **Course Prerequisite Tracking**
```python
def track_course_prerequisites():
    """Track course prerequisites and suggest optimal scheduling."""
    
    prerequisites = {
        "CIS 5420": ["CIS 5410"],  # Networks 2 requires Networks 1
        "CIS 5898": ["CIS 5600", "CIS 5410"]  # Projects requires Security and Networks 1
    }
    
    courses = get_personal_course_schedule()
    
    for course in courses:
        if course.course_code in prerequisites:
            required_courses = prerequisites[course.course_code]
            completed = [c.course_code for c in courses if c.status == CourseStatus.COMPLETED]
            
            missing_prereqs = [req for req in required_courses if req not in completed]
            if missing_prereqs:
                print(f"⚠️  {course.course_code} requires: {', '.join(missing_prereqs)}")
```

### **Integration with Academic Tools**

#### **Study Schedule Generator**
```python
def generate_study_schedule():
    """Generate study schedule based on course deadlines."""
    
    app = AcademicCalendarApp()
    courses = get_personal_course_schedule()
    
    # Get all deadlines for enrolled courses
    enrolled_courses = [c for c in courses if c.status == CourseStatus.REGISTERED]
    
    study_schedule = []
    for course in enrolled_courses:
        term_deadlines = app.filter_events(term=course.term, event_type="deadline")
        
        for deadline in term_deadlines:
            # Create study sessions leading up to deadline
            for days_before in [14, 7, 3, 1]:
                study_date = deadline.date - timedelta(days=days_before)
                if study_date >= date.today():
                    study_schedule.append({
                        'date': study_date,
                        'course': course.course_code,
                        'task': f"Study for {deadline.title}",
                        'deadline': deadline.date
                    })
    
    return sorted(study_schedule, key=lambda x: x['date'])
```

#### **Academic Performance Tracking**
```python
def track_academic_performance():
    """Track academic performance and suggest improvements."""
    
    courses = get_personal_course_schedule()
    
    # Calculate GPA for completed courses
    completed_courses = [c for c in courses if c.status == CourseStatus.COMPLETED and c.grade]
    
    grade_points = {'A': 4.0, 'B': 3.0, 'C': 2.0, 'D': 1.0, 'F': 0.0}
    
    if completed_courses:
        total_points = sum(grade_points.get(c.grade, 0) * c.credits for c in completed_courses)
        total_credits = sum(c.credits for c in completed_courses)
        gpa = total_points / total_credits if total_credits > 0 else 0
        
        print(f"📊 Current GPA: {gpa:.2f}")
        print(f"📚 Completed Credits: {total_credits}")
        
        # Suggest improvements
        if gpa < 3.0:
            print("💡 Consider study groups or tutoring for upcoming courses")
```

**The Academic Calendar Integration Guide provides comprehensive workflows for advanced usage, data management, and system integration to maximize your academic planning efficiency!** 🔗
