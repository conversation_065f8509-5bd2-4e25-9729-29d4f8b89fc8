# 🔧 Academic Calendar Application - Technical Documentation

## 📋 **Table of Contents**
1. [System Architecture](#system-architecture)
2. [File Structure](#file-structure)
3. [Data Models](#data-models)
4. [API Reference](#api-reference)
5. [Import/Export Specifications](#importexport-specifications)
6. [Error Handling](#error-handling)
7. [Testing Framework](#testing-framework)

---

## 🏗️ **System Architecture**

### **Core Components**

```
Academic Calendar Application
├── Data Layer
│   ├── AcademicCalendarEvent (data model)
│   ├── PersonalCourse (course tracking)
│   └── Event categorization system
├── Business Logic Layer
│   ├── AcademicCalendarApp (main application)
│   ├── Filtering and search engine
│   └── Event management
├── Integration Layer
│   ├── AcademicCalendarGenerator (iCal export)
│   ├── AcademicCalendarImporter (file import)
│   └── AcademicCalendarGoogleIntegration (Google Calendar)
└── Presentation Layer
    ├── Command-line interface
    ├── IntegratedAcademicView (unified display)
    └── Personal schedule management
```

### **Design Patterns**
- **Factory Pattern**: Event creation and categorization
- **Strategy Pattern**: Different display formats (table, list, summary)
- **Observer Pattern**: Event filtering and search
- **Adapter Pattern**: External calendar integration

---

## 📁 **File Structure**

```
academic-calendar/
├── Core Application
│   ├── academic_calendar.py                    # Main application entry point
│   ├── academic_calendar_events.py             # Event data and models
│   └── academic_calendar_app.py                # Application logic
├── Data Management
│   ├── academic_calendar_generator.py          # iCal file generation
│   ├── academic_calendar_importer.py           # File import functionality
│   └── personal_course_schedule.py             # Personal course tracking
├── Integration
│   ├── academic_calendar_google_integration.py # Google Calendar API
│   ├── academic_calendar_setup.py              # Google setup wizard
│   └── integrated_academic_view.py             # Unified view system
├── Testing
│   ├── academic_calendar_test.py               # Core functionality tests
│   ├── academic_calendar_test_import.py        # Import functionality tests
│   ├── academic_calendar_test_oauth.py         # OAuth authentication tests
│   └── filter_validation_test.py               # Filter validation tests
├── Configuration
│   ├── requirements.txt                        # Python dependencies
│   ├── credentials.json                        # Google API credentials
│   └── token.json                              # Google OAuth token
├── Generated Files
│   ├── academic_calendar_2025_2026_complete.ics
│   ├── academic_calendar_[Term].ics            # Term-specific calendars
│   └── spring_2_2026_events.csv               # Sample import file
└── Documentation
    ├── ACADEMIC_CALENDAR_USER_MANUAL.md
    ├── ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md
    └── Various guide files
```

---

## 📊 **Data Models**

### **AcademicCalendarEvent**

```python
@dataclass
class AcademicCalendarEvent:
    title: str                                    # Event title
    date: date                                    # Event date
    event_type: AcademicCalendarEventType        # Event classification
    category: AcademicCalendarEventCategory      # Event category
    description: str = ""                        # Detailed description
    term: str = ""                              # Academic term
    reminder_days: List[int] = None             # Reminder schedule
```

**Automatic Reminder Assignment:**
- **Deadlines**: [7, 1] (7 days and 1 day before)
- **Class Dates**: [1] (1 day before)
- **Registration**: [7] (7 days before)
- **Holidays**: [] (no reminders)

### **AcademicCalendarEventType Enum**

```python
class AcademicCalendarEventType(Enum):
    DEADLINE = "deadline"        # Academic deadlines
    CLASS_DATE = "class_date"    # Class-related dates
    HOLIDAY = "holiday"          # University holidays
    REGISTRATION = "registration" # Registration periods
```

### **AcademicCalendarEventCategory Enum**

```python
class AcademicCalendarEventCategory(Enum):
    PETITION = "petition"        # Graduation petitions
    APPLICATION = "application"  # Application deadlines
    REGISTRATION = "registration" # Registration events
    PAYMENT = "payment"          # Payment deadlines
    CLASS_START = "class_start"  # Class beginning dates
    WITHDRAWAL = "withdrawal"    # Withdrawal deadlines
    HOLIDAY = "holiday"          # Holiday events
    CLASS_END = "class_end"      # Class ending dates
    COMMENCEMENT = "commencement" # Graduation ceremonies
```

### **PersonalCourse**

```python
@dataclass
class PersonalCourse:
    course_code: str             # Course identifier (e.g., "CIS 5600")
    course_title: str            # Full course name
    term: str                    # Academic term
    status: CourseStatus         # Enrollment status
    credits: int = 3             # Credit hours
    grade: Optional[str] = None  # Final grade
    notes: str = ""              # Additional notes
```

### **CourseStatus Enum**

```python
class CourseStatus(Enum):
    REGISTERED = "registered"    # Currently enrolled
    PLANNED = "planned"          # Future enrollment
    COMPLETED = "completed"      # Successfully finished
    DROPPED = "dropped"          # Withdrawn/cancelled
```

---

## 🔌 **API Reference**

### **Core Application Class**

#### **AcademicCalendarApp**

```python
class AcademicCalendarApp:
    def __init__(self):
        """Initialize application with event data."""
        
    def filter_events(self, event_type: str = None, term: str = None, 
                     category: str = None) -> List[AcademicCalendarEvent]:
        """Filter events by multiple criteria."""
        
    def get_upcoming_deadlines(self, days: int = 7) -> List[AcademicCalendarEvent]:
        """Get deadlines within specified days."""
        
    def display_events(self, events: List[AcademicCalendarEvent] = None, 
                      format_type: str = "table"):
        """Display events in specified format."""
```

### **Event Management Functions**

#### **Data Access**

```python
def get_academic_calendar_events() -> List[AcademicCalendarEvent]:
    """Return all academic calendar events."""
    
def get_academic_calendar_events_by_type(event_type: AcademicCalendarEventType) -> List[AcademicCalendarEvent]:
    """Filter events by type."""
    
def get_academic_calendar_events_by_term(term: str) -> List[AcademicCalendarEvent]:
    """Filter events by term."""
```

### **Calendar Generation**

#### **AcademicCalendarGenerator**

```python
class AcademicCalendarGenerator:
    def generate_ical_content(self, events: List[AcademicCalendarEvent]) -> str:
        """Generate iCal format content."""
        
    def save_ical_file(self, events: List[AcademicCalendarEvent], filename: str) -> str:
        """Save events to iCal file."""
        
    def create_separate_files_by_term(self, events: List[AcademicCalendarEvent]) -> List[str]:
        """Create term-specific calendar files."""
        
    def create_combined_file(self, events: List[AcademicCalendarEvent]) -> str:
        """Create single file with all events."""
```

### **Import Functionality**

#### **AcademicCalendarImporter**

```python
class AcademicCalendarImporter:
    def import_from_file(self, file_path: str, existing_events: List[AcademicCalendarEvent]) -> Tuple[List[AcademicCalendarEvent], Dict]:
        """Import events from external file."""
        
    def import_from_csv(self, file_path: str) -> List[AcademicCalendarEvent]:
        """Import from CSV file."""
        
    def import_from_ical(self, file_path: str) -> List[AcademicCalendarEvent]:
        """Import from iCal file."""
        
    def detect_duplicates(self, new_events: List[AcademicCalendarEvent], 
                         existing_events: List[AcademicCalendarEvent]) -> List[AcademicCalendarEvent]:
        """Detect and filter duplicate events."""
```

### **Google Calendar Integration**

#### **AcademicCalendarGoogleIntegration**

```python
class AcademicCalendarGoogleIntegration:
    def authenticate(self) -> bool:
        """Authenticate with Google Calendar API."""
        
    def add_event(self, event: AcademicCalendarEvent, calendar_id: str = 'primary') -> bool:
        """Add single event to Google Calendar."""
        
    def add_all_events(self, events: List[AcademicCalendarEvent], 
                      calendar_id: str = 'primary') -> Dict[str, int]:
        """Add multiple events to Google Calendar."""
        
    def create_calendar(self, name: str, description: str) -> Optional[str]:
        """Create new Google Calendar."""
        
    def list_calendars(self) -> List[Dict[str, str]]:
        """List available Google Calendars."""
```

### **Personal Course Management**

#### **Course Functions**

```python
def get_personal_course_schedule() -> List[PersonalCourse]:
    """Return personal course schedule."""
    
def get_courses_by_term(term: str) -> List[PersonalCourse]:
    """Filter courses by term."""
    
def get_courses_by_status(status: CourseStatus) -> List[PersonalCourse]:
    """Filter courses by status."""
    
def display_course_schedule(courses: Optional[List[PersonalCourse]] = None):
    """Display course schedule."""
```

---

## 📄 **Import/Export Specifications**

### **iCal (.ics) Export Format**

#### **File Structure**
```
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Academic Calendar//Academic Events//EN
CALSCALE:GREGORIAN
METHOD:PUBLISH
X-WR-CALNAME:Academic Calendar 2025-2026
X-WR-CALDESC:University Academic Calendar Events
X-WR-TIMEZONE:America/Los_Angeles

BEGIN:VEVENT
UID:[unique-identifier]
DTSTAMP:[creation-timestamp]
DTSTART;VALUE=DATE:[event-date]
SUMMARY:[event-title]
DESCRIPTION:[event-description]
CATEGORIES:[event-type],[category]
STATUS:CONFIRMED
TRANSP:TRANSPARENT
BEGIN:VALARM
ACTION:DISPLAY
DESCRIPTION:Reminder: Academic deadline
TRIGGER:-P[days]D
END:VALARM
END:VEVENT

END:VCALENDAR
```

#### **Metadata Fields**
- **UID**: UUID4 unique identifier
- **DTSTAMP**: ISO format creation timestamp
- **DTSTART**: DATE format (YYYYMMDD)
- **CATEGORIES**: Comma-separated event type and category
- **VALARM**: Multiple alarms based on event type

### **CSV Import Format**

#### **Required Columns**
```csv
title,date,description,event_type,category,term
```

#### **Field Specifications**
- **title**: String, event title (required)
- **date**: ISO date format YYYY-MM-DD (required)
- **description**: String, detailed description (optional)
- **event_type**: Enum value from AcademicCalendarEventType (required)
- **category**: Enum value from AcademicCalendarEventCategory (required)
- **term**: String, academic term identifier (required)

#### **Valid Enum Values**
**event_type**: `deadline`, `class_date`, `holiday`, `registration`
**category**: `petition`, `application`, `registration`, `payment`, `class_start`, `withdrawal`, `holiday`, `class_end`, `commencement`

#### **Example CSV**
```csv
title,date,description,event_type,category,term
"Final Exam Period","2025-12-15","Final examinations begin","class_date","class_end","Fall 2 - 2025"
"Registration Deadline","2025-08-13","Last day to register","deadline","registration","Fall 1 - 2025"
"Labor Day Holiday","2025-09-01","University closed","holiday","holiday","Fall 1 - 2025"
```

### **Data Validation Rules**

#### **Date Validation**
- Must be valid ISO date format (YYYY-MM-DD)
- Must be within reasonable academic year range
- Future dates preferred for planning

#### **Enum Validation**
- event_type must match AcademicCalendarEventType values
- category must match AcademicCalendarEventCategory values
- Case-insensitive matching supported

#### **Duplicate Detection**
Events are considered duplicates if they match:
- Same title (case-insensitive)
- Same date
- Same term

---

## ⚠️ **Error Handling**

### **Exception Hierarchy**

```python
class AcademicCalendarError(Exception):
    """Base exception for Academic Calendar application."""
    
class ImportError(AcademicCalendarError):
    """Raised when import operations fail."""
    
class ValidationError(AcademicCalendarError):
    """Raised when data validation fails."""
    
class GoogleCalendarError(AcademicCalendarError):
    """Raised when Google Calendar operations fail."""
```

### **Error Handling Patterns**

#### **File Operations**
```python
try:
    events = importer.import_from_file(file_path)
except FileNotFoundError:
    print(f"❌ File not found: {file_path}")
except PermissionError:
    print(f"❌ Permission denied: {file_path}")
except ImportError as e:
    print(f"❌ Import failed: {str(e)}")
```

#### **Google Calendar Integration**
```python
try:
    integration = AcademicCalendarGoogleIntegration()
    results = integration.add_all_events(events)
except GoogleCalendarError as e:
    print(f"❌ Google Calendar sync failed: {str(e)}")
    print("You can manually run the setup later.")
```

#### **Data Validation**
```python
try:
    event = AcademicCalendarEvent(title, date, event_type, category)
except ValidationError as e:
    print(f"❌ Invalid event data: {str(e)}")
    continue  # Skip invalid event
```

### **Logging Configuration**

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('academic_calendar.log'),
        logging.StreamHandler()
    ]
)
```

---

## 🧪 **Testing Framework**

### **Test Structure**

```
tests/
├── academic_calendar_test.py           # Core functionality
├── academic_calendar_test_import.py    # Import functionality
├── academic_calendar_test_oauth.py     # OAuth authentication
└── filter_validation_test.py          # Filter validation
```

### **Test Categories**

#### **Unit Tests**
- Event data validation
- Filter functionality
- Date handling
- Enum validation

#### **Integration Tests**
- File import/export
- Google Calendar API
- Command-line interface
- Calendar generation

#### **Validation Tests**
- Event count verification
- Filter result validation
- Data consistency checks
- Cross-reference validation

### **Running Tests**

```bash
# Core functionality
python academic_calendar_test.py

# Import functionality
python academic_calendar_test_import.py

# OAuth authentication
python academic_calendar_test_oauth.py

# Comprehensive filter validation
python filter_validation_test.py
```

### **Test Data**

#### **Sample Events**
```python
test_events = [
    AcademicCalendarEvent(
        title="Test Deadline",
        date=date(2025, 8, 15),
        event_type=AcademicCalendarEventType.DEADLINE,
        category=AcademicCalendarEventCategory.REGISTRATION,
        term="Fall 1 - 2025"
    )
]
```

#### **Expected Results**
- Total events: 50
- Event types: 4 (deadline: 28, class_date: 10, holiday: 8, registration: 4)
- Terms: 6 (Fall 1-2025: 10, Fall 2-2025: 13, Spring 1-2026: 10, Spring 2-2026: 13, Summer 2026: 3, Fall 1-2026: 1)
- Categories: 9 (petition: 6, application: 6, registration: 8, payment: 4, class_start: 4, withdrawal: 8, holiday: 8, class_end: 4, commencement: 2)

---

## 🔧 **Development Guidelines**

### **Code Style**
- Follow PEP 8 Python style guidelines
- Use type hints for all function parameters and return values
- Document all classes and functions with docstrings
- Use descriptive variable and function names

### **Adding New Events**
1. Add event to `academic_calendar_events.py`
2. Update test expectations in test files
3. Regenerate calendar files
4. Update documentation if new categories added

### **Extending Functionality**
1. Create new modules following naming convention
2. Add appropriate error handling
3. Include comprehensive tests
4. Update documentation and user manual

**The Academic Calendar application provides a robust, extensible platform for academic calendar management with comprehensive technical documentation for developers and maintainers.** 🔧
