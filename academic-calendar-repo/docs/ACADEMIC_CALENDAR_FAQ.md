# ❓ Academic Calendar Application - FAQ & Troubleshooting

## 📋 **Frequently Asked Questions**

### **🎯 General Usage**

#### **Q: How many academic events are included in the system?**
A: The Academic Calendar Application includes **50 academic events** covering the 2025-2026 academic year across 6 terms (Fall 1-2025, Fall 2-2025, Spring 1-2026, Spring 2-2026, Summer 2026, Fall 1-2026).

#### **Q: Can I track my personal course schedule?**
A: Yes! The system includes personal course tracking for your specific courses. Currently configured for 4 CIS courses:
- CIS 5600 (Fall 1 - 2025) - Registered
- CIS 5410 (Fall 2 - 2025) - Planned  
- CIS 5420 (Spring 1 - 2026) - Planned
- CIS 5898 (Spring 2 - 2026) - Planned

#### **Q: What calendar applications are supported?**
A: The system generates standard iCal (.ics) files compatible with:
- ✅ Google Calendar
- ✅ Apple Calendar
- ✅ Microsoft Outlook
- ✅ Thunderbird
- ✅ Any RFC 5545 compliant calendar application

#### **Q: Can I import my own events?**
A: Yes! You can import events using:
- **CSV files** with the required format
- **iCal (.ics) files** from other calendar systems
- **Interactive import wizard**: `python academic_calendar_importer.py`

---

### **🔍 Filtering and Search**

#### **Q: How do I filter events for my specific courses?**
```bash
# View events for your registered course (Fall 1 - 2025)
python academic_calendar.py --term "Fall 1 - 2025"

# View events for your planned course (Spring 2 - 2026)
python academic_calendar.py --term "Spring 2 - 2026"

# View all your course terms
python academic_calendar.py --term "Fall 1" --term "Fall 2" --term "Spring 1" --term "Spring 2"
```

#### **Q: What filter options are available?**
- **Event Types**: `deadline`, `class_date`, `holiday`, `registration`
- **Categories**: `petition`, `application`, `registration`, `payment`, `class_start`, `withdrawal`, `holiday`, `class_end`, `commencement`
- **Terms**: Exact match or partial matching (case insensitive)

#### **Q: How do I see upcoming deadlines?**
```bash
# Next 7 days (default)
python academic_calendar.py --action upcoming

# Next 30 days
python academic_calendar.py --action upcoming --days 30

# Upcoming registration deadlines
python academic_calendar.py --action upcoming --days 60 --category registration
```

---

### **📅 Calendar Generation and Export**

#### **Q: How do I generate calendar files?**
```bash
python academic_calendar.py --action generate
```
This creates 7 calendar files:
- 1 combined file with all events
- 6 term-specific files

#### **Q: How do I import calendar files into my calendar app?**
- **Google Calendar**: Settings → Import & Export → Select file
- **Apple Calendar**: Double-click the .ics file
- **Outlook**: File → Import/Export → Select .ics file

#### **Q: Do the calendar files include reminders?**
Yes! Automatic reminders are set based on event type:
- **Deadlines**: 7 days and 1 day before
- **Class Dates**: 1 day before
- **Registration**: 7 days before
- **Holidays**: No reminders

---

### **🌐 Google Calendar Integration**

#### **Q: How do I set up Google Calendar integration?**
1. Get Google API credentials from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Google Calendar API
3. Download `credentials.json` to the application directory
4. Run: `python academic_calendar_setup.py`

#### **Q: What if Google Calendar setup fails?**
1. Verify `credentials.json` exists in the application directory
2. Check that Google Calendar API is enabled in Google Cloud Console
3. Test authentication: `python academic_calendar_test_oauth.py`
4. Delete `token.json` and retry setup if needed

#### **Q: Can I sync to a specific Google Calendar?**
Yes! During setup, you can:
- Choose an existing calendar
- Create a new calendar specifically for academic events
- Sync to your primary calendar

---

## 🔧 **Troubleshooting Guide**

### **🚨 Common Error Messages**

#### **"ModuleNotFoundError: No module named 'academic_calendar_events'"**
**Cause**: Virtual environment not activated
**Solution**:
```bash
source calendar_env/bin/activate
```
**Verification**: You should see `(calendar_env)` in your terminal prompt

#### **"No events found matching your criteria"**
**Cause**: Incorrect filter parameters
**Solutions**:
1. Check spelling of term names
2. Use partial matching: `--term "Fall"` instead of exact term
3. Verify event types: `deadline`, `class_date`, `holiday`, `registration`
4. Check available terms: `python academic_calendar.py --format summary`

#### **"FileNotFoundError: [Errno 2] No such file or directory: 'credentials.json'"**
**Cause**: Google Calendar credentials not found
**Solution**:
1. Download credentials from Google Cloud Console
2. Save as `credentials.json` in application directory
3. Verify file exists: `ls -la credentials.json`

#### **"ImportError: Failed to parse CSV file"**
**Cause**: Incorrect CSV format
**Solution**:
1. Check required columns: `title,date,description,event_type,category,term`
2. Verify date format: `YYYY-MM-DD`
3. Check enum values for event_type and category
4. Use sample file: `spring_2_2026_events.csv`

---

### **🔍 Diagnostic Commands**

#### **System Health Check**
```bash
# Verify total event count (should be 50)
python academic_calendar.py --format summary

# Test core functionality
python academic_calendar_test.py

# Validate all filters
python filter_validation_test.py

# Test import functionality
python academic_calendar_test_import.py
```

#### **Google Calendar Diagnostics**
```bash
# Test OAuth authentication
python academic_calendar_test_oauth.py

# Check credentials file
ls -la credentials.json token.json

# Reset Google authentication
rm token.json && python academic_calendar_setup.py
```

#### **Data Validation**
```bash
# Check event distribution
python academic_calendar.py --format summary

# Verify personal course schedule
python personal_course_schedule.py

# Test integrated view
python integrated_academic_view.py
```

---

### **🔄 Reset and Recovery Procedures**

#### **Complete System Reset**
```bash
# 1. Regenerate all calendar files
python academic_calendar.py --action generate

# 2. Reset Google Calendar authentication
rm token.json

# 3. Verify system integrity
python academic_calendar_test.py

# 4. Check event count
python academic_calendar.py --format summary
```

#### **Data Recovery**
```bash
# Restore from backup (if available)
cp backup_YYYYMMDD_HHMMSS/* .

# Regenerate calendar files
python academic_calendar.py --action generate

# Verify data integrity
python filter_validation_test.py
```

#### **Google Calendar Re-setup**
```bash
# 1. Remove existing tokens
rm token.json

# 2. Verify credentials
ls -la credentials.json

# 3. Run setup wizard
python academic_calendar_setup.py

# 4. Test authentication
python academic_calendar_test_oauth.py
```

---

### **📊 Performance and Optimization**

#### **Q: The application seems slow. How can I optimize it?**
1. **Check event count**: Should be 50 events
2. **Verify filters**: Use specific filters to reduce result sets
3. **Clear old files**: Remove unnecessary .ics files
4. **Update Python**: Ensure Python 3.8+ is installed

#### **Q: Calendar files are too large. Can I reduce them?**
```bash
# Generate term-specific files instead of combined
python academic_calendar.py --action generate

# Use only the term files you need
# Delete the combined file if not needed
rm academic_calendar_2025_2026_complete.ics
```

#### **Q: How do I backup my data?**
```bash
# Manual backup
cp academic_calendar_events.py academic_calendar_events_backup.py
cp personal_course_schedule.py personal_course_schedule_backup.py

# Backup calendar files
mkdir backup_$(date +%Y%m%d)
cp *.ics backup_$(date +%Y%m%d)/
```

---

### **🎯 Best Practices**

#### **Daily Usage**
1. **Check upcoming deadlines weekly**: `python academic_calendar.py --action upcoming`
2. **Use term-specific filters**: Focus on your current term
3. **Keep personal schedule updated**: Update course status as you register
4. **Generate fresh calendar files each term**: Ensure latest data

#### **Data Management**
1. **Regular backups**: Backup before making changes
2. **Version control**: Use git for tracking changes
3. **Validate after changes**: Run tests after modifications
4. **Document customizations**: Note any personal modifications

#### **Integration Tips**
1. **Use Google Calendar for reminders**: Automatic sync across devices
2. **Import term-specific files**: Avoid calendar clutter
3. **Set up email notifications**: Configure calendar app notifications
4. **Regular sync**: Update calendar files periodically

---

### **📞 Getting Additional Help**

#### **Self-Help Resources**
1. **User Manual**: `ACADEMIC_CALENDAR_USER_MANUAL.md`
2. **Quick Reference**: `ACADEMIC_CALENDAR_QUICK_REFERENCE.md`
3. **Technical Documentation**: `ACADEMIC_CALENDAR_TECHNICAL_DOCUMENTATION.md`
4. **Integration Guide**: `ACADEMIC_CALENDAR_INTEGRATION_GUIDE.md`

#### **Validation Steps**
```bash
# Complete system validation
python academic_calendar_test.py
python academic_calendar_test_import.py
python filter_validation_test.py

# Expected results:
# - 50 total events
# - All tests pass
# - No import errors
```

#### **System Information**
- **Academic Year**: 2025-2026
- **Total Events**: 50
- **Personal Courses**: 4 CIS courses
- **Supported Formats**: iCal, CSV
- **Integration**: Google Calendar API

**Your Academic Calendar Application is designed to be reliable and user-friendly. Most issues can be resolved with the solutions provided above!** ❓
