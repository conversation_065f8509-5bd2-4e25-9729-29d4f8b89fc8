# 🔧 OAuth "Access Blocked" Error Fix

## 🚨 **Error**: "Access blocked: PythtonCal has not completed the Google verification process"

## ⚡ **Quick Fix (5 minutes)**

### **Step 1: Open OAuth Consent Screen**
- URL: https://console.cloud.google.com/apis/credentials/consent
- Make sure you're in the correct project: `academic-calendar-461611`

### **Step 2: Edit Your App**
1. Click **"Edit App"** button
2. Navigate through the setup steps until you reach **"Test users"**

### **Step 3: Add Test Users**
1. In the **"Test users"** section, click **"+ Add Users"**
2. Enter your email address (the one you're using for authentication)
3. Click **"Add"**
4. Click **"Save and Continue"**

### **Step 4: Verify Settings**
Make sure your OAuth consent screen has:
- ✅ **User Type**: External
- ✅ **Publishing Status**: Testing (not "In production")
- ✅ **Your email** listed under Test users
- ✅ **Scopes**: `https://www.googleapis.com/auth/calendar`

### **Step 5: Try Authentication Again**
```bash
# Kill the current setup process
# Then restart it
source calendar_env/bin/activate
python setup_google_calendar.py
```

---

## 🔄 **Alternative Solutions**

### **Option A: Internal App (Google Workspace Only)**
If you have Google Workspace:
1. Change **User Type** to **"Internal"**
2. Internal apps don't need verification
3. Only works with Google Workspace accounts

### **Option B: Simplified Scope**
If issues persist:
1. Remove all scopes except: `https://www.googleapis.com/auth/calendar`
2. Ensure no additional permissions are requested

### **Option C: New Project**
If all else fails:
1. Create a completely new Google Cloud project
2. Use a simpler app name (avoid special characters)
3. Set up as Internal if possible

---

## 🎯 **Expected Results After Fix**

Once you add yourself as a test user:
- ✅ Google will show a warning but allow you to proceed
- ✅ You'll see "Go to [App Name] (unsafe)" link
- ✅ Click "Advanced" → "Go to [App Name] (unsafe)"
- ✅ Grant calendar permissions
- ✅ Setup will complete successfully

---

## 🔍 **Verification Steps**

After making changes:
1. **Wait 2-3 minutes** for changes to propagate
2. **Use incognito/private browser** for testing
3. **Clear browser cache** if needed
4. **Try authentication again**

---

## 🆘 **If Still Having Issues**

1. **Check project selection** in Google Cloud Console
2. **Verify email spelling** in test users
3. **Try different browser** or incognito mode
4. **Wait 5-10 minutes** for Google's systems to update

**The key is adding your email as a test user - this bypasses the verification requirement!**
