# Academic Calendar Application

A comprehensive Python application for managing university academic calendar events. This application parses academic dates, generates calendar files, and provides various viewing and filtering options.

## Features

- ✅ **Parse academic dates and events** with proper categorization
- ✅ **Generate iCal (.ics) files** for universal calendar compatibility
- ✅ **Import external calendar files** (iCal, CSV formats)
- ✅ **Set appropriate reminders** for different event types
- ✅ **Filter and display events** in multiple formats
- ✅ **Command-line interface** for easy automation
- ✅ **Google Calendar integration** (optional)

## Quick Start

### 1. Basic Usage (No Dependencies Required)

```bash
# Display all events in table format
python academic_calendar.py

# Generate calendar files
python academic_calendar.py --action generate

# Show upcoming deadlines
python academic_calendar.py --action upcoming --days 14

# Import external calendar file
python academic_calendar.py --action import --import-file calendar.ics
```

### 2. Generate Calendar Files

```bash
python ical_generator.py
```

This creates:
- `academic_calendar_fall_2025_complete.ics` - All events
- `academic_calendar_Fall_1_-_2025.ics` - Fall 1 term only
- `academic_calendar_Fall_2_-_2025.ics` - Fall 2 term only
- `academic_calendar_Spring_2026.ics` - Spring registration

### 3. Import to Your Calendar

**Apple Calendar (macOS/iOS):**
- Double-click the `.ics` file
- Choose which calendar to import to

**Google Calendar:**
- Go to Google Calendar → Settings → Import & Export
- Choose the `.ics` file and select target calendar

**Outlook:**
- File → Open & Export → Import/Export
- Select the `.ics` file

**Other Calendar Apps:**
- Most calendar applications support `.ics` import

## Event Types and Reminders

The application automatically sets appropriate reminders:

| Event Type | Reminder Schedule | Examples |
|------------|------------------|----------|
| **Deadlines** | 7 days + 1 day before | Registration, Payment, Petition deadlines |
| **Class Dates** | 1 day before | Classes begin, Last day of classes |
| **Registration** | 7 days before | Registration opens |
| **Holidays** | No reminders | Labor Day, University closed |

## Import External Calendar Files

Import events from other calendar systems:

```bash
# Import with file browser
python academic_calendar.py --action import

# Import specific file
python academic_calendar.py --action import --import-file calendar.ics

# Interactive import wizard
python academic_calendar_importer.py
```

**Supported Formats:**
- **iCal (.ics)**: Standard calendar format from Google Calendar, Outlook, Apple Calendar
- **CSV (.csv)**: Spreadsheet format with title, date, description columns

**Features:**
- ✅ Automatic event categorization and term assignment
- ✅ Duplicate detection and prevention
- ✅ Integration with existing academic calendar
- ✅ Optional Google Calendar sync
- ✅ Comprehensive error handling

## Command Line Options

```bash
python academic_calendar.py [OPTIONS]

Options:
  --action {display,generate,filter,upcoming,import}
                        Action to perform (default: display)
  --format {table,list,summary}
                        Display format (default: table)
  --type {deadline,class_date,holiday,registration}
                        Filter by event type
  --term TERM           Filter by term (partial match)
  --category {petition,application,registration,payment,class_start,withdrawal,holiday,class_end}
                        Filter by category
  --days DAYS           Number of days for upcoming deadlines (default: 7)
  --import-file FILE    Path to calendar file to import (.ics or .csv)
```

### Examples

```bash
# Show only deadlines in detailed format
python academic_calendar.py --type deadline --format list

# Show Fall 1 events only
python academic_calendar.py --term "Fall 1" --format table

# Show upcoming deadlines for next 30 days
python academic_calendar.py --action upcoming --days 30

# Show summary statistics
python academic_calendar.py --format summary

# Filter by category
python academic_calendar.py --category registration --format list
```

## Google Calendar Integration (Optional)

For direct Google Calendar integration:

### Setup

1. **Install Google packages:**
   ```bash
   pip install google-auth google-auth-oauthlib google-api-python-client
   ```

2. **Get Google Calendar API credentials:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create/select a project
   - Enable Google Calendar API
   - Create OAuth 2.0 Client ID credentials
   - Download `credentials.json` to this directory

3. **Run setup:**
   ```bash
   python academic_calendar_setup.py
   ```

### Usage

The setup script will:
- Authenticate with Google Calendar
- Optionally create a new calendar
- Add all academic events with proper reminders

## File Structure

```
academic-calendar/
├── academic_calendar.py                    # Main application
├── academic_calendar_events.py             # Event data and structures
├── academic_calendar_generator.py          # iCal file generation
├── academic_calendar_importer.py           # Calendar import functionality
├── academic_calendar_google_integration.py # Google Calendar API (optional)
├── academic_calendar_setup.py              # Google Calendar setup
├── requirements.txt                        # Dependencies
└── README.md                               # This file
```

## Academic Events Included

### Fall 1 - 2025 Term
- August 1: Last day to Petition to Graduate
- August 5: Last day to apply
- August 13: Last day to register
- August 15: Last day for payment
- August 18: Classes begin
- August 24: Last day to withdraw with full refund
- September 1: Labor Day (University Closed)
- September 28: Last day to withdraw with W grade
- October 12: Last day of classes
- October 13: Holiday (University Closed)

### Fall 2 - 2025 Term
- September 26: Last day to Petition to Graduate
- October 6: Last day to apply

### Spring 2026 Registration
- November 10: First day to register for Spring 1 and 2

## Customization

### Adding New Events

Edit `calendar_events.py` and add events to the `get_fall_2025_events()` function:

```python
AcademicEvent(
    title="Your Event Title",
    date=date(2025, 12, 15),
    event_type=EventType.DEADLINE,
    category=EventCategory.APPLICATION,
    description="Event description",
    term="Your Term"
)
```

### Changing Reminder Settings

Modify the `__post_init__` method in `AcademicEvent` class or set custom `reminder_days` when creating events.

### Timezone Settings

Update the timezone in `ical_generator.py`:

```python
self.timezone = "America/New_York"  # Change as needed
```

## Troubleshooting

### Common Issues

1. **Google Calendar authentication fails:**
   - Ensure `credentials.json` is in the correct directory
   - Check that Google Calendar API is enabled
   - Verify OAuth consent screen is configured

2. **Calendar import doesn't work:**
   - Ensure the `.ics` file is properly formatted
   - Try importing a smaller file first
   - Check your calendar app's import documentation

3. **Reminders not showing:**
   - Some calendar apps may not support all reminder types
   - Check your calendar app's notification settings

### Getting Help

If you encounter issues:
1. Check the error messages for specific guidance
2. Verify all file paths and permissions
3. Ensure your calendar application supports iCal format
4. For Google Calendar issues, check the Google Calendar API documentation

## License

This project is provided as-is for educational and personal use.
