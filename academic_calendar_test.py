#!/usr/bin/env python3
"""
Test script for the Academic Calendar Application
Verifies that all components work correctly.
"""

import os
import sys
from datetime import date
from academic_calendar_events import get_academic_calendar_events, AcademicCalendarEventType, AcademicCalendarEventCategory
from academic_calendar_generator import AcademicCalendarGenerator
from academic_calendar import AcademicCalendarApp


def test_events_data():
    """Test that events data is loaded correctly."""
    print("Testing events data...")
    
    events = get_academic_calendar_events()
    assert len(events) == 50, f"Expected 50 events, got {len(events)}"

    # Test event types
    deadlines = [e for e in events if e.event_type == AcademicCalendarEventType.DEADLINE]
    class_dates = [e for e in events if e.event_type == AcademicCalendarEventType.CLASS_DATE]
    holidays = [e for e in events if e.event_type == AcademicCalendarEventType.HOLIDAY]
    registration = [e for e in events if e.event_type == AcademicCalendarEventType.REGISTRATION]

    assert len(deadlines) == 28, f"Expected 28 deadlines, got {len(deadlines)}"
    assert len(class_dates) == 10, f"Expected 10 class dates, got {len(class_dates)}"
    assert len(holidays) == 8, f"Expected 8 holidays, got {len(holidays)}"
    assert len(registration) == 4, f"Expected 4 registration events, got {len(registration)}"
    
    # Test specific dates
    first_event = min(events, key=lambda x: x.date)
    last_event = max(events, key=lambda x: x.date)

    assert first_event.date == date(2025, 8, 1), f"First event should be Aug 1, got {first_event.date}"
    assert last_event.date == date(2026, 5, 10), f"Last event should be May 10, 2026, got {last_event.date}"
    
    print("✅ Events data test passed")


def test_ical_generation():
    """Test iCal file generation."""
    print("Testing iCal generation...")
    
    generator = AcademicCalendarGenerator()
    events = get_academic_calendar_events()
    
    # Test content generation
    content = generator.generate_ical_content(events)
    assert content.startswith("BEGIN:VCALENDAR"), "iCal should start with BEGIN:VCALENDAR"
    assert content.endswith("END:VCALENDAR"), "iCal should end with END:VCALENDAR"
    assert "SUMMARY:" in content, "iCal should contain event summaries"
    assert "DTSTART;" in content, "iCal should contain event dates"
    
    # Test file creation
    test_filename = "test_calendar.ics"
    generator.save_ical_file(events, test_filename)
    assert os.path.exists(test_filename), f"File {test_filename} should be created"
    
    # Clean up
    os.remove(test_filename)
    
    print("✅ iCal generation test passed")


def test_app_functionality():
    """Test main application functionality."""
    print("Testing application functionality...")
    
    app = AcademicCalendarApp()
    
    # Test filtering
    deadlines = app.filter_events(event_type="deadline")
    assert len(deadlines) == 28, f"Expected 28 deadlines, got {len(deadlines)}"

    fall1_2025_events = app.filter_events(term="Fall 1 - 2025")
    assert len(fall1_2025_events) == 10, f"Expected 10 Fall 1 - 2025 events, got {len(fall1_2025_events)}"
    
    # Test upcoming deadlines (should be 0 since events are in 2025)
    upcoming = app.get_upcoming_deadlines(days=30)
    assert len(upcoming) == 0, f"Expected 0 upcoming deadlines, got {len(upcoming)}"
    
    print("✅ Application functionality test passed")


def test_reminder_logic():
    """Test that reminders are set correctly."""
    print("Testing reminder logic...")
    
    events = get_academic_calendar_events()
    
    for event in events:
        if event.event_type == AcademicCalendarEventType.DEADLINE:
            assert 7 in event.reminder_days, f"Deadline {event.title} should have 7-day reminder"
            assert 1 in event.reminder_days, f"Deadline {event.title} should have 1-day reminder"
        elif event.event_type == AcademicCalendarEventType.CLASS_DATE:
            assert 1 in event.reminder_days, f"Class date {event.title} should have 1-day reminder"
        elif event.event_type == AcademicCalendarEventType.REGISTRATION:
            assert 7 in event.reminder_days, f"Registration {event.title} should have 7-day reminder"
        elif event.event_type == AcademicCalendarEventType.HOLIDAY:
            assert len(event.reminder_days) == 0, f"Holiday {event.title} should have no reminders"
    
    print("✅ Reminder logic test passed")


def test_file_generation():
    """Test that all expected files are generated."""
    print("Testing file generation...")
    
    # Clean up any existing files
    test_files = [
        "academic_calendar_fall_2025_complete.ics",
        "academic_calendar_Fall_1_-_2025.ics",
        "academic_calendar_Fall_2_-_2025.ics",
        "academic_calendar_Spring_2026.ics"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
    
    # Generate files
    generator = AcademicCalendarGenerator()
    events = get_academic_calendar_events()
    
    combined_file = generator.create_combined_file(events)
    term_files = generator.create_separate_files_by_term(events)
    
    # Check files exist
    assert os.path.exists(combined_file), f"Combined file {combined_file} should exist"
    
    for file in term_files:
        assert os.path.exists(file), f"Term file {file} should exist"
    
    # Check file contents
    with open(combined_file, 'r') as f:
        content = f.read()
        assert "BEGIN:VCALENDAR" in content, "Combined file should be valid iCal"
        assert "Last day to Petition to Graduate" in content, "Should contain petition events"
        assert "CLASSES BEGIN" in content, "Should contain class start event"
    
    print("✅ File generation test passed")


def run_all_tests():
    """Run all tests."""
    print("Running Academic Calendar Application Tests")
    print("=" * 50)
    
    try:
        test_events_data()
        test_ical_generation()
        test_app_functionality()
        test_reminder_logic()
        test_file_generation()
        
        print("\n🎉 All tests passed successfully!")
        print("\nThe Academic Calendar Application is working correctly.")
        print("You can now:")
        print("1. Import the generated .ics files into your calendar")
        print("2. Use the command-line interface to view and filter events")
        print("3. Optionally set up Google Calendar integration")
        
        return True
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
