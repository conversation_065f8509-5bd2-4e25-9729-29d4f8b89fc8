# 📚 Academic Calendar Application - User Manual

## 📋 **Table of Contents**
1. [Introduction](#introduction)
2. [Installation and Setup](#installation-and-setup)
3. [Basic Usage](#basic-usage)
4. [Filtering and Searching](#filtering-and-searching)
5. [Calendar File Management](#calendar-file-management)
6. [Personal Course Schedule](#personal-course-schedule)
7. [Google Calendar Integration](#google-calendar-integration)
8. [Troubleshooting](#troubleshooting)

---

## 🎯 **Introduction**

The Academic Calendar Application is a comprehensive tool for managing university academic calendar events, personal course schedules, and calendar integration. It provides:

- **50+ Academic Events** across multiple terms (Fall 2025 - Summer 2026)
- **Personal Course Tracking** with status management
- **Flexible Filtering** by term, type, and category
- **Calendar Export** to .ics files for any calendar application
- **Google Calendar Integration** for automatic synchronization
- **Import Functionality** for external calendar files

### **System Requirements**
- Python 3.8 or higher
- Terminal/Command Prompt access
- Internet connection (for Google Calendar integration only)

---

## 🔧 **Installation and Setup**

### **Step 1: Environment Setup**

1. **Navigate to the application directory:**
   ```bash
   cd /path/to/academic-calendar
   ```

2. **Activate the virtual environment:**
   ```bash
   source calendar_env/bin/activate
   ```
   
   You should see `(calendar_env)` in your terminal prompt.

3. **Verify installation:**
   ```bash
   python academic_calendar.py --help
   ```

### **Step 2: First Run Test**

Test the application with a basic command:
```bash
python academic_calendar.py --format summary
```

**Expected Output:**
```
Academic Calendar Summary
==============================
Total Events: 50

By Event Type:
  Deadline: 28
  Class_Date: 10
  Holiday: 8
  Registration: 4

By Term:
  Fall 1 - 2025: 10
  Fall 2 - 2025: 13
  Spring 1 - 2026: 10
  Spring 2 - 2026: 13
  Summer 2026: 3
  Fall 1 - 2026: 1
```

---

## 🎯 **Basic Usage**

### **Command Structure**
```bash
python academic_calendar.py [OPTIONS]
```

### **Display All Events**
```bash
# Table format (default)
python academic_calendar.py

# List format (detailed)
python academic_calendar.py --format list

# Summary format (statistics)
python academic_calendar.py --format summary
```

### **Generate Calendar Files**
```bash
python academic_calendar.py --action generate
```

This creates 7 calendar files:
- `academic_calendar_2025_2026_complete.ics` (all events)
- `academic_calendar_Fall_1_-_2025.ics`
- `academic_calendar_Fall_2_-_2025.ics`
- `academic_calendar_Spring_1_-_2026.ics`
- `academic_calendar_Spring_2_-_2026.ics`
- `academic_calendar_Summer_2026.ics`
- `academic_calendar_Fall_1_-_2026.ics`

### **Import External Calendar**
```bash
# Import specific file
python academic_calendar.py --action import --import-file calendar.csv

# Interactive import wizard
python academic_calendar.py --action import
```

---

## 🔍 **Filtering and Searching**

### **Filter by Event Type**
```bash
# Show only deadlines
python academic_calendar.py --type deadline

# Show only class dates
python academic_calendar.py --type class_date

# Show only holidays
python academic_calendar.py --type holiday

# Show only registration events
python academic_calendar.py --type registration
```

### **Filter by Academic Term**
```bash
# Exact term match
python academic_calendar.py --term "Fall 1 - 2025"

# Partial term matching (case insensitive)
python academic_calendar.py --term "Fall"      # All Fall terms
python academic_calendar.py --term "spring"    # All Spring terms
python academic_calendar.py --term "2026"      # All 2026 terms
```

### **Filter by Category**
```bash
# Academic deadlines
python academic_calendar.py --category petition
python academic_calendar.py --category application
python academic_calendar.py --category registration
python academic_calendar.py --category payment

# Academic events
python academic_calendar.py --category class_start
python academic_calendar.py --category class_end
python academic_calendar.py --category withdrawal
python academic_calendar.py --category holiday
python academic_calendar.py --category commencement
```

### **Combined Filters**
```bash
# Fall 1 deadlines only
python academic_calendar.py --term "Fall 1" --type deadline

# Spring registration events
python academic_calendar.py --term "Spring" --category registration

# Holiday events with detailed format
python academic_calendar.py --type holiday --format list
```

### **Upcoming Deadlines**
```bash
# Next 7 days (default)
python academic_calendar.py --action upcoming

# Next 30 days
python academic_calendar.py --action upcoming --days 30

# Next 14 days with detailed format
python academic_calendar.py --action upcoming --days 14 --format list
```

---

## 📅 **Calendar File Management**

### **Supported File Formats**

#### **iCal (.ics) Files**
- **Standard format** compatible with all calendar applications
- **Automatic reminders** based on event types
- **Proper categorization** for easy organization

#### **CSV Files**
Required columns:
```csv
title,date,description,event_type,category,term
"Event Title","2025-08-15","Event description","deadline","registration","Fall 1 - 2025"
```

**Event Types:** `deadline`, `class_date`, `holiday`, `registration`
**Categories:** `petition`, `application`, `registration`, `payment`, `class_start`, `withdrawal`, `holiday`, `class_end`, `commencement`

### **Import Calendar Files**

#### **Method 1: Command Line**
```bash
python academic_calendar.py --action import --import-file myevents.csv
```

#### **Method 2: Interactive Import**
```bash
python academic_calendar_importer.py
```

This opens a file browser (if available) or prompts for file path.

### **Export to Calendar Applications**

#### **Google Calendar**
1. Go to Google Calendar → Settings → Import & Export
2. Click "Select file from your computer"
3. Choose any `.ics` file from the application
4. Select destination calendar
5. Click "Import"

#### **Apple Calendar**
1. Double-click any `.ics` file
2. Choose destination calendar
3. Click "OK"

#### **Microsoft Outlook**
1. File → Open & Export → Import/Export
2. Select "Import an iCalendar (.ics) or vCalendar file"
3. Browse and select `.ics` file
4. Choose destination calendar

---

## 📚 **Personal Course Schedule**

### **View Your Course Schedule**
```bash
python personal_course_schedule.py
```

**Output Example:**
```
📚 Personal Course Schedule
==================================================

📅 Fall 1 - 2025:
  ✅ CIS 5600: Information Security Management (Fall 1 - 2025)
  Credits: 3

📅 Fall 2 - 2025:
  📅 CIS 5410: Computer Networks for Information Specialists 1 (Fall 2 - 2025)
  Credits: 3

📊 Total Credits: 12
📋 Status Summary: {'registered': 1, 'planned': 3}
```

### **Integrated Academic View**
```bash
python integrated_academic_view.py
```

This provides:
- **Complete academic overview** with statistics
- **Term-specific views** showing both courses and academic events
- **Upcoming deadlines** with course context
- **Comprehensive planning** information

### **Course Status Indicators**
- ✅ **Registered**: Currently enrolled
- 📅 **Planned**: Intended for future enrollment
- 🎓 **Completed**: Successfully finished
- ❌ **Dropped**: Withdrawn or cancelled

---

## 🌐 **Google Calendar Integration**

### **Prerequisites**
1. Google account with Google Calendar access
2. Google Cloud Console project (free)
3. OAuth 2.0 credentials

### **Setup Process**

#### **Step 1: Get Google Calendar API Credentials**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project: "Academic Calendar Integration"
3. Enable Google Calendar API:
   - Go to "APIs & Services" → "Library"
   - Search for "Google Calendar API"
   - Click "Enable"
4. Create OAuth 2.0 Client ID credentials:
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "OAuth client ID"
   - Choose "Desktop application"
   - Download as `credentials.json`
5. Place `credentials.json` in the application directory

#### **Step 2: Run Setup Wizard**
```bash
python academic_calendar_setup.py
```

The setup wizard will:
- ✅ Authenticate with your Google account
- ✅ Optionally create a new calendar
- ✅ Add all 50 academic events with smart reminders
- ✅ Sync across all your devices

#### **Step 3: Test Authentication**
```bash
python academic_calendar_test_oauth.py
```

### **Smart Reminders**
The integration automatically sets reminders based on event types:
- **Deadlines**: 7 days and 1 day before
- **Class Dates**: 1 day before
- **Registration**: 7 days before
- **Holidays**: No reminders

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **"ModuleNotFoundError" Error**
**Problem**: Virtual environment not activated
**Solution**:
```bash
source calendar_env/bin/activate
```

#### **"No events found" Message**
**Problem**: Incorrect filter criteria
**Solutions**:
- Check spelling of term names
- Use partial matching: `--term "Fall"` instead of exact term
- Verify event types: `deadline`, `class_date`, `holiday`, `registration`

#### **Google Calendar Setup Issues**
**Problem**: OAuth authentication fails
**Solutions**:
1. Verify `credentials.json` exists in application directory
2. Check Google Cloud Console project settings
3. Ensure Google Calendar API is enabled
4. Test with: `python academic_calendar_test_oauth.py`

#### **Import Issues**
**Problem**: CSV import fails
**Solutions**:
1. Check CSV format matches required columns
2. Verify date format: `YYYY-MM-DD`
3. Ensure event_type and category values are valid
4. Test with sample file: `python academic_calendar.py --action import --import-file sample_calendar.csv`

### **Getting Help**

#### **View Available Options**
```bash
python academic_calendar.py --help
```

#### **Test Core Functionality**
```bash
python academic_calendar_test.py
```

#### **Test Import Functionality**
```bash
python academic_calendar_test_import.py
```

#### **Validate Filtering**
```bash
python filter_validation_test.py
```

### **File Locations**
- **Application files**: Current directory
- **Generated calendars**: Current directory (`.ics` files)
- **Google credentials**: `credentials.json` and `token.json`
- **Personal schedule**: `personal_course_schedule.py`

---

## 📞 **Support and Resources**

### **Quick Commands Reference**
| Task | Command |
|------|---------|
| View all events | `python academic_calendar.py` |
| Generate calendars | `python academic_calendar.py --action generate` |
| Filter by term | `python academic_calendar.py --term "Fall 1"` |
| Show deadlines | `python academic_calendar.py --type deadline` |
| Import file | `python academic_calendar.py --action import --import-file file.csv` |
| Personal schedule | `python personal_course_schedule.py` |
| Integrated view | `python integrated_academic_view.py` |
| Google setup | `python academic_calendar_setup.py` |

### **File Formats**
- **Calendar Export**: `.ics` (iCalendar standard)
- **Import Formats**: `.ics`, `.csv`
- **Personal Data**: Python modules (`.py`)

### **Success Indicators**
- ✅ Summary shows 50 total events
- ✅ Calendar generation creates 7 files
- ✅ Import functionality detects duplicates
- ✅ Google Calendar sync adds events with reminders
- ✅ Personal schedule shows your courses

**Your Academic Calendar application is ready for comprehensive academic planning!** 🎓
