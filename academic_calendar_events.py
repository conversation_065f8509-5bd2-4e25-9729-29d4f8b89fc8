"""
Academic Calendar Events Data Structure
Contains all the academic calendar events with proper categorization.
"""

from datetime import datetime, date
from dataclasses import dataclass
from typing import List, Optional
from enum import Enum


class AcademicCalendarEventType(Enum):
    DEADLINE = "deadline"
    CLASS_DATE = "class_date"
    HOLIDAY = "holiday"
    REGISTRATION = "registration"


class AcademicCalendarEventCategory(Enum):
    PETITION = "petition"
    APPLICATION = "application"
    REGISTRATION = "registration"
    PAYMENT = "payment"
    CLASS_START = "class_start"
    WITHDRAWAL = "withdrawal"
    HOLIDAY = "holiday"
    CLASS_END = "class_end"
    COMMENCEMENT = "commencement"


@dataclass
class AcademicCalendarEvent:
    title: str
    date: date
    event_type: AcademicCalendarEventType
    category: AcademicCalendarEventCategory
    description: str = ""
    term: str = ""
    reminder_days: List[int] = None

    def __post_init__(self):
        if self.reminder_days is None:
            # Set default reminders based on event type
            if self.event_type == AcademicCalendarEventType.DEADLINE:
                self.reminder_days = [7, 1]  # 1 week and 1 day before
            elif self.event_type == AcademicCalendarEventType.CLASS_DATE:
                self.reminder_days = [1]  # 1 day before
            elif self.event_type == AcademicCalendarEventType.REGISTRATION:
                self.reminder_days = [7]  # 1 week before
            else:  # HOLIDAY
                self.reminder_days = []  # No reminders for holidays


def get_academic_calendar_events() -> List[AcademicCalendarEvent]:
    """
    Returns all academic calendar events for 2025-2026 academic year.
    """
    events = [
        # Fall 1 - 2025 Term Events
        AcademicCalendarEvent(
            title="Last day to Petition to Graduate for Fall 1 – 2025",
            date=date(2025, 8, 1),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Fall 1 term",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Fall 1 - 2025",
            date=date(2025, 8, 5),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Fall 1 term",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to register for Fall 1 - 2025",
            date=date(2025, 8, 13),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Final deadline to register for Fall 1 classes",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day for payment for Fall 1 – 2025",
            date=date(2025, 8, 15),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PAYMENT,
            description="Final deadline to complete payment for Fall 1 term",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="CLASSES BEGIN (Monday)",
            date=date(2025, 8, 18),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_START,
            description="First day of classes for Fall 1 term",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with full refund",
            date=date(2025, 8, 24),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from classes and receive full refund",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (Labor Day) (University Closed)",
            date=date(2025, 9, 1),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Labor Day - University closed",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with a W and no refund",
            date=date(2025, 9, 28),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from classes with W grade (no refund)",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day of classes (Sunday)",
            date=date(2025, 10, 12),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_END,
            description="Final day of classes for Fall 1 term",
            term="Fall 1 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (University Closed)",
            date=date(2025, 10, 13),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="University closed",
            term="Fall 1 - 2025"
        ),
        
        # Fall 2 - 2025 Term Events
        AcademicCalendarEvent(
            title="Last day to Petition to Graduate for Fall 2 – 2025",
            date=date(2025, 9, 26),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Fall 2 term",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Fall 2 - 2025",
            date=date(2025, 10, 7),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Fall 2 term",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to register for Fall 2 - 2025",
            date=date(2025, 10, 15),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Final deadline to register for Fall 2 classes",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day for payment for Fall 2 - 2025",
            date=date(2025, 10, 16),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PAYMENT,
            description="Final deadline to complete payment for Fall 2 term",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="CLASSES BEGIN (Monday) - Fall 2",
            date=date(2025, 10, 20),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_START,
            description="First day of classes for Fall 2 term",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with full refund - Fall 2",
            date=date(2025, 10, 26),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Fall 2 classes and receive full refund",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (Veterans Day) (University Closed)",
            date=date(2025, 11, 11),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Veterans Day - University closed",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (Thanksgiving) (University Closed) - Day 1",
            date=date(2025, 11, 26),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Thanksgiving Holiday - University closed",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (Thanksgiving) (University Closed) - Day 2",
            date=date(2025, 11, 27),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Thanksgiving Holiday - University closed",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Holiday (Thanksgiving) (University Closed) - Day 3",
            date=date(2025, 11, 28),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Thanksgiving Holiday - University closed",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with a W and no refund - Fall 2",
            date=date(2025, 11, 30),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Fall 2 classes with W grade (no refund)",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Fall Commencement Exercises (Saturday)",
            date=date(2025, 12, 13),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.COMMENCEMENT,
            description="Fall 2025 graduation ceremony",
            term="Fall 2 - 2025"
        ),
        AcademicCalendarEvent(
            title="Last day of classes (Sunday) - Fall 2",
            date=date(2025, 12, 14),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_END,
            description="Final day of classes for Fall 2 term",
            term="Fall 2 - 2025"
        ),
        
        # Spring 1 - 2026 Term Events
        AcademicCalendarEvent(
            title="First day to register for Spring 1 and 2 – 2026",
            date=date(2025, 11, 10),
            event_type=AcademicCalendarEventType.REGISTRATION,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Registration opens for Spring 1 and 2 terms 2026",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to Petition to Graduate for Spring 1 – 2026",
            date=date(2025, 11, 21),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Spring 1 term",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Spring 1 - 2026",
            date=date(2025, 12, 30),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Spring 1 term",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to register for Spring 1 - 2026",
            date=date(2026, 1, 7),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Final deadline to register for Spring 1 classes",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day for payment for Spring 1 - 2026",
            date=date(2026, 1, 8),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PAYMENT,
            description="Final deadline to complete payment for Spring 1 term",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Classes begin (Monday) - Spring 1",
            date=date(2026, 1, 12),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_START,
            description="First day of classes for Spring 1 term",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with full refund - Spring 1",
            date=date(2026, 1, 18),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Spring 1 classes and receive full refund",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Holiday (Martin Luther King Jr. Day) (University Closed)",
            date=date(2026, 1, 19),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Martin Luther King Jr. Day - University closed",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="First day to register for Summer 2026",
            date=date(2026, 2, 2),
            event_type=AcademicCalendarEventType.REGISTRATION,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Registration opens for Summer 2026 term",
            term="Summer 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to petition to graduate for Spring 2 - 2026",
            date=date(2026, 2, 6),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with a W and no refund - Spring 1",
            date=date(2026, 2, 22),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Spring 1 classes with W grade (no refund)",
            term="Spring 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Spring 2 - 2026",
            date=date(2026, 3, 2),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day of classes (Sunday) - Spring 1",
            date=date(2026, 3, 8),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_END,
            description="Final day of classes for Spring 1 term",
            term="Spring 1 - 2026"
        ),

        # Spring 2 - 2026 Term Events
        AcademicCalendarEvent(
            title="First day to register for Spring 2, 2026",
            date=date(2025, 11, 10),
            event_type=AcademicCalendarEventType.REGISTRATION,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Registration opens for Spring 2, 2026 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to petition to graduate for Spring 2, 2026",
            date=date(2026, 2, 13),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Spring 2, 2026",
            date=date(2026, 3, 3),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to register for Spring 2, 2026",
            date=date(2026, 3, 11),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Final deadline to register for Spring 2 classes",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day for payment for Spring 2, 2026",
            date=date(2026, 3, 12),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PAYMENT,
            description="Final deadline to complete payment for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Classes begin (Monday) - Spring 2",
            date=date(2026, 3, 16),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_START,
            description="First day of classes for Spring 2 term",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with full refund - Spring 2",
            date=date(2026, 3, 22),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Spring 2 classes and receive full refund",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Holiday (Friday, spring break) (university closed)",
            date=date(2026, 3, 27),
            event_type=AcademicCalendarEventType.HOLIDAY,
            category=AcademicCalendarEventCategory.HOLIDAY,
            description="Spring break - University closed",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="First day to register for Fall 1 and 2, 2026",
            date=date(2026, 4, 6),
            event_type=AcademicCalendarEventType.REGISTRATION,
            category=AcademicCalendarEventCategory.REGISTRATION,
            description="Registration opens for Fall 1 and 2, 2026 terms",
            term="Fall 1 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to petition to graduate for Summer 2026",
            date=date(2026, 4, 24),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.PETITION,
            description="Final deadline to submit graduation petition for Summer 2026 term",
            term="Summer 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to withdraw from a class with a W and no refund - Spring 2",
            date=date(2026, 4, 26),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.WITHDRAWAL,
            description="Final deadline to withdraw from Spring 2 classes with W grade (no refund)",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day to apply for Summer 2026",
            date=date(2026, 5, 4),
            event_type=AcademicCalendarEventType.DEADLINE,
            category=AcademicCalendarEventCategory.APPLICATION,
            description="Final deadline to submit application for Summer 2026 term",
            term="Summer 2026"
        ),
        AcademicCalendarEvent(
            title="Spring commencement exercises (Saturday)",
            date=date(2026, 5, 9),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.COMMENCEMENT,
            description="Spring 2026 graduation ceremony",
            term="Spring 2 - 2026"
        ),
        AcademicCalendarEvent(
            title="Last day of classes (Sunday) - Spring 2",
            date=date(2026, 5, 10),
            event_type=AcademicCalendarEventType.CLASS_DATE,
            category=AcademicCalendarEventCategory.CLASS_END,
            description="Final day of classes for Spring 2 term",
            term="Spring 2 - 2026"
        ),
    ]
    
    return events


def get_academic_calendar_events_by_type(event_type: AcademicCalendarEventType) -> List[AcademicCalendarEvent]:
    """Get events filtered by type."""
    return [event for event in get_academic_calendar_events() if event.event_type == event_type]


def get_academic_calendar_events_by_term(term: str) -> List[AcademicCalendarEvent]:
    """Get events filtered by term."""
    return [event for event in get_academic_calendar_events() if event.term == term]


# Backward compatibility functions
def get_events_by_type(event_type: AcademicCalendarEventType) -> List[AcademicCalendarEvent]:
    """Backward compatibility function."""
    return get_academic_calendar_events_by_type(event_type)


def get_events_by_term(term: str) -> List[AcademicCalendarEvent]:
    """Backward compatibility function."""
    return get_academic_calendar_events_by_term(term)


def get_fall_2025_events() -> List[AcademicCalendarEvent]:
    """Backward compatibility function - returns all academic events."""
    return get_academic_calendar_events()


def get_academic_events() -> List[AcademicCalendarEvent]:
    """Backward compatibility function - returns all academic events."""
    return get_academic_calendar_events()
