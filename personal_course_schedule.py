#!/usr/bin/env python3
"""
Personal Course Schedule
Tracks individual course enrollments and academic progress.
"""

from datetime import date
from dataclasses import dataclass
from typing import List, Optional
from enum import Enum


class CourseStatus(Enum):
    REGISTERED = "registered"
    PLANNED = "planned"
    COMPLETED = "completed"
    DROPPED = "dropped"


@dataclass
class PersonalCourse:
    course_code: str
    course_title: str
    term: str
    status: CourseStatus
    credits: int = 3
    grade: Optional[str] = None
    notes: str = ""
    
    def __str__(self):
        status_emoji = {
            CourseStatus.REGISTERED: "✅",
            CourseStatus.PLANNED: "📅",
            CourseStatus.COMPLETED: "🎓",
            CourseStatus.DROPPED: "❌"
        }
        return f"{status_emoji[self.status]} {self.course_code}: {self.course_title} ({self.term})"


def get_personal_course_schedule() -> List[PersonalCourse]:
    """
    Returns the personal course schedule for the academic program.
    """
    courses = [
        PersonalCourse(
            course_code="CIS 5600",
            course_title="Information Security Management",
            term="Fall 1 - 2025",
            status=CourseStatus.REGISTERED,
            credits=3,
            notes="Already registered for Fall 1 - 2025"
        ),
        PersonalCourse(
            course_code="CIS 5410",
            course_title="Computer Networks for Information Specialists 1",
            term="Fall 2 - 2025",
            status=CourseStatus.PLANNED,
            credits=3,
            notes="Planned for Fall 2 - 2025"
        ),
        PersonalCourse(
            course_code="CIS 5420",
            course_title="Computer Networks for Information Specialists 2",
            term="Spring 1 - 2026",
            status=CourseStatus.PLANNED,
            credits=3,
            notes="Planned for Spring 1 - 2026"
        ),
        PersonalCourse(
            course_code="CIS 5898",
            course_title="Projects in Computer Information Systems",
            term="Spring 2 - 2026",
            status=CourseStatus.PLANNED,
            credits=3,
            notes="Planned for Spring 2 - 2026"
        )
    ]
    
    return courses


def get_courses_by_term(term: str) -> List[PersonalCourse]:
    """Get courses filtered by term."""
    return [course for course in get_personal_course_schedule() if term.lower() in course.term.lower()]


def get_courses_by_status(status: CourseStatus) -> List[PersonalCourse]:
    """Get courses filtered by status."""
    return [course for course in get_personal_course_schedule() if course.status == status]


def display_course_schedule(courses: Optional[List[PersonalCourse]] = None):
    """Display the course schedule in a formatted way."""
    if courses is None:
        courses = get_personal_course_schedule()
    
    if not courses:
        print("No courses found.")
        return
    
    print("📚 Personal Course Schedule")
    print("=" * 50)
    
    # Group by term
    terms = {}
    for course in courses:
        if course.term not in terms:
            terms[course.term] = []
        terms[course.term].append(course)
    
    total_credits = 0
    for term in sorted(terms.keys()):
        print(f"\n📅 {term}:")
        term_credits = 0
        for course in terms[term]:
            print(f"  {course}")
            if course.status != CourseStatus.DROPPED:
                term_credits += course.credits
        print(f"  Credits: {term_credits}")
        total_credits += term_credits
    
    print(f"\n📊 Total Credits: {total_credits}")
    
    # Status summary
    status_counts = {}
    for status in CourseStatus:
        count = len([c for c in courses if c.status == status])
        if count > 0:
            status_counts[status.value] = count
    
    print(f"📋 Status Summary: {status_counts}")


def get_course_schedule_summary():
    """Get a summary of the course schedule."""
    courses = get_personal_course_schedule()
    
    summary = {
        'total_courses': len(courses),
        'total_credits': sum(c.credits for c in courses if c.status != CourseStatus.DROPPED),
        'by_status': {},
        'by_term': {}
    }
    
    for status in CourseStatus:
        summary['by_status'][status.value] = len([c for c in courses if c.status == status])
    
    for course in courses:
        if course.term not in summary['by_term']:
            summary['by_term'][course.term] = 0
        if course.status != CourseStatus.DROPPED:
            summary['by_term'][course.term] += 1
    
    return summary


if __name__ == "__main__":
    display_course_schedule()
