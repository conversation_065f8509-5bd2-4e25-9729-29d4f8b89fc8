# Google Calendar Integration Setup Guide

## 📋 **Prerequisites**
✅ Google Calendar packages installed  
✅ Google account with access to Google Calendar  
✅ Web browser access to Google Cloud Console  

## 🔧 **Step-by-Step Setup**

### **Step 1: Create Google Cloud Project**

1. **Go to Google Cloud Console** (already opened in your browser)
   - URL: https://console.cloud.google.com/

2. **Create a new project or select existing one:**
   - Click the project dropdown at the top
   - Click "New Project"
   - Name: `Academic Calendar Integration`
   - Click "Create"

### **Step 2: Enable Google Calendar API**

1. **Navigate to APIs & Services:**
   - In the left sidebar, click "APIs & Services" → "Library"
   - Or use this direct link: https://console.cloud.google.com/apis/library

2. **Search and enable Google Calendar API:**
   - Search for "Google Calendar API"
   - Click on "Google Calendar API"
   - Click "Enable"

### **Step 3: Configure OAuth Consent Screen**

1. **Go to OAuth consent screen:**
   - Left sidebar: "APIs & Services" → "OAuth consent screen"
   - Or direct link: https://console.cloud.google.com/apis/credentials/consent

2. **Configure the consent screen:**
   - User Type: Select "External" (unless you have Google Workspace)
   - Click "Create"

3. **Fill in required information:**
   - App name: `Academic Calendar Integration`
   - User support email: Your email
   - Developer contact information: Your email
   - Click "Save and Continue"

4. **Scopes (Step 2):**
   - Click "Add or Remove Scopes"
   - Search for "calendar" 
   - Select: `https://www.googleapis.com/auth/calendar`
   - Click "Update" then "Save and Continue"

5. **Test users (Step 3):**
   - Add your email address as a test user
   - Click "Save and Continue"

6. **Summary (Step 4):**
   - Review and click "Back to Dashboard"

### **Step 4: Create OAuth 2.0 Credentials**

1. **Go to Credentials:**
   - Left sidebar: "APIs & Services" → "Credentials"
   - Or direct link: https://console.cloud.google.com/apis/credentials

2. **Create OAuth 2.0 Client ID:**
   - Click "+ Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: "Desktop application"
   - Name: `Academic Calendar Desktop App`
   - Click "Create"

3. **Download credentials:**
   - A popup will show your client ID and secret
   - Click "Download JSON"
   - Save the file as `credentials.json` in your project directory

### **Step 5: Place Credentials File**

1. **Move the downloaded file:**
   - Rename the downloaded file to exactly `credentials.json`
   - Place it in the same directory as your Python files
   - The file should be in: `/Users/<USER>/Documents/augment-projects/Tasks/credentials.json`

## 🚀 **Step 6: Run the Setup**

Once you have the `credentials.json` file in place, run:

```bash
# Activate the virtual environment
source calendar_env/bin/activate

# Run the Google Calendar setup
python google_calendar_integration.py
```

## 🔍 **What Happens During Setup**

1. **Authentication:**
   - A browser window will open
   - Sign in to your Google account
   - Grant permission to access your calendar
   - The app will save authentication tokens

2. **Calendar Creation:**
   - Option to create a new calendar for academic events
   - Or use your primary calendar

3. **Event Import:**
   - All 13 academic events will be added to Google Calendar
   - Proper reminders will be set automatically
   - Events will be categorized and color-coded

## ⚠️ **Important Security Notes**

- **Keep `credentials.json` secure** - Don't share or commit to version control
- **The `token.json` file** will be created automatically - also keep secure
- **Revoke access** anytime from: https://myaccount.google.com/permissions

## 🎯 **Expected Results**

After successful setup, you'll have:
- ✅ All 13 academic events in Google Calendar
- ✅ Smart reminders (7 days + 1 day for deadlines)
- ✅ Proper event categorization
- ✅ Synchronized across all your devices

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **"credentials.json not found"**
   - Ensure the file is in the correct directory
   - Check the filename is exactly `credentials.json`

2. **"Access blocked" error**
   - Make sure OAuth consent screen is configured
   - Add your email as a test user
   - Try using an incognito browser window

3. **"API not enabled" error**
   - Verify Google Calendar API is enabled in Cloud Console
   - Wait a few minutes for changes to propagate

4. **Permission denied**
   - Check that you granted calendar access during authentication
   - Try deleting `token.json` and re-authenticating

### **Need Help?**
- Check the Google Calendar API documentation
- Verify your Google Cloud project settings
- Ensure you're using the correct Google account

## 📱 **Next Steps After Setup**

Once setup is complete:
1. Check your Google Calendar for the new events
2. Verify reminders are working
3. Events will sync to your phone/tablet automatically
4. You can modify events in Google Calendar if needed

---

**Ready to proceed?** Make sure you have the `credentials.json` file, then run the setup command!
