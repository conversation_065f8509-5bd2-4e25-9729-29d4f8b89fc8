"""
Academic Calendar Google Calendar API Integration (Optional)
Provides functionality to directly add academic calendar events to Google Calendar.

Note: Requires Google Calendar API credentials and setup.
This is an optional feature - the iCal files work with all calendar systems.
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False

from academic_calendar_events import AcademicCalendarEvent, get_academic_calendar_events


class AcademicCalendarGoogleIntegration:
    """
    Google Calendar API integration for academic events.
    
    Setup Instructions:
    1. Go to Google Cloud Console (https://console.cloud.google.com/)
    2. Create a new project or select existing one
    3. Enable Google Calendar API
    4. Create credentials (OAuth 2.0 Client ID)
    5. Download credentials.json file to this directory
    6. Install required packages: pip install google-auth google-auth-oauthlib google-api-python-client
    """
    
    SCOPES = ['https://www.googleapis.com/auth/calendar']
    
    def __init__(self, credentials_file: str = 'credentials.json', 
                 token_file: str = 'token.json'):
        if not GOOGLE_AVAILABLE:
            raise ImportError(
                "Google Calendar integration requires additional packages. "
                "Install with: pip install google-auth google-auth-oauthlib google-api-python-client"
            )
        
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Calendar API."""
        creds = None
        
        # Load existing token
        if os.path.exists(self.token_file):
            creds = Credentials.from_authorized_user_file(self.token_file, self.SCOPES)
        
        # If no valid credentials, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                if not os.path.exists(self.credentials_file):
                    raise FileNotFoundError(
                        f"Credentials file '{self.credentials_file}' not found. "
                        "Please download it from Google Cloud Console."
                    )
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, self.SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Save credentials for next run
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())
        
        self.service = build('calendar', 'v3', credentials=creds)
    
    def create_calendar(self, name: str, description: str = "") -> str:
        """
        Create a new calendar and return its ID.
        """
        calendar_body = {
            'summary': name,
            'description': description,
            'timeZone': 'America/Los_Angeles'  # Adjust as needed
        }
        
        try:
            created_calendar = self.service.calendars().insert(body=calendar_body).execute()
            return created_calendar['id']
        except HttpError as error:
            print(f"Error creating calendar: {error}")
            return None
    
    def add_event(self, event: AcademicCalendarEvent, calendar_id: str = 'primary') -> Optional[str]:
        """
        Add a single academic event to Google Calendar.
        Returns the event ID if successful.
        """
        # Convert date to datetime for all-day event
        event_date = event.date.strftime('%Y-%m-%d')
        
        google_event = {
            'summary': event.title,
            'description': f"{event.description}\n\nTerm: {event.term}\nCategory: {event.category.value}",
            'start': {
                'date': event_date,
                'timeZone': 'America/Los_Angeles',
            },
            'end': {
                'date': event_date,
                'timeZone': 'America/Los_Angeles',
            },
            'reminders': {
                'useDefault': False,
                'overrides': []
            }
        }
        
        # Add custom reminders
        for days in event.reminder_days:
            google_event['reminders']['overrides'].append({
                'method': 'popup',
                'minutes': days * 24 * 60  # Convert days to minutes
            })
        
        try:
            created_event = self.service.events().insert(
                calendarId=calendar_id, 
                body=google_event
            ).execute()
            return created_event['id']
        except HttpError as error:
            print(f"Error adding event '{event.title}': {error}")
            return None
    
    def add_all_events(self, events: List[AcademicCalendarEvent], 
                      calendar_id: str = 'primary') -> Dict[str, Any]:
        """
        Add all academic events to Google Calendar.
        Returns summary of results.
        """
        results = {
            'successful': 0,
            'failed': 0,
            'event_ids': [],
            'errors': []
        }
        
        for event in events:
            event_id = self.add_event(event, calendar_id)
            if event_id:
                results['successful'] += 1
                results['event_ids'].append(event_id)
            else:
                results['failed'] += 1
                results['errors'].append(f"Failed to add: {event.title}")
        
        return results
    
    def list_calendars(self) -> List[Dict[str, str]]:
        """List all available calendars."""
        try:
            calendars_result = self.service.calendarList().list().execute()
            calendars = calendars_result.get('items', [])
            
            return [
                {
                    'id': cal['id'],
                    'name': cal['summary'],
                    'description': cal.get('description', ''),
                    'primary': cal.get('primary', False)
                }
                for cal in calendars
            ]
        except HttpError as error:
            print(f"Error listing calendars: {error}")
            return []


def setup_academic_calendar_google_integration():
    """
    Interactive setup for Academic Calendar Google Calendar integration.
    """
    print("Academic Calendar Google Calendar Integration Setup")
    print("=" * 50)

    if not GOOGLE_AVAILABLE:
        print("❌ Google Calendar packages not installed.")
        print("Install with: pip install google-auth google-auth-oauthlib google-api-python-client")
        return False

    if not os.path.exists('credentials.json'):
        print("❌ credentials.json file not found.")
        print("\nSetup Instructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create/select a project")
        print("3. Enable Google Calendar API")
        print("4. Create OAuth 2.0 credentials")
        print("5. Download credentials.json to this directory")
        return False

    try:
        integration = AcademicCalendarGoogleIntegration()
        print("✅ Academic Calendar Google Calendar authentication successful!")
        
        # List existing calendars
        calendars = integration.list_calendars()
        print(f"\nFound {len(calendars)} calendars:")
        for i, cal in enumerate(calendars):
            primary = " (Primary)" if cal['primary'] else ""
            print(f"  {i+1}. {cal['name']}{primary}")

        # Ask user if they want to create a new calendar
        create_new = input("\nCreate new calendar for academic events? (y/n): ").lower() == 'y'

        target_calendar = 'primary'
        selected_calendar_name = "Primary Calendar"

        if create_new:
            calendar_name = input("Calendar name [Academic Calendar 2025-2026]: ") or "Academic Calendar 2025-2026"
            calendar_description = "University Academic Calendar Events for 2025-2026 Academic Year"

            calendar_id = integration.create_calendar(calendar_name, calendar_description)
            if calendar_id:
                print(f"✅ Created calendar: {calendar_name}")
                target_calendar = calendar_id
                selected_calendar_name = calendar_name
            else:
                print("❌ Failed to create calendar. Using primary calendar.")
                target_calendar = 'primary'
                selected_calendar_name = "Primary Calendar"
        else:
            # Let user choose from existing calendars
            print("\nSelect which calendar to add events to:")
            for i, cal in enumerate(calendars):
                primary = " (Primary)" if cal['primary'] else ""
                print(f"  {i+1}. {cal['name']}{primary}")

            while True:
                try:
                    choice = input(f"\nEnter calendar number (1-{len(calendars)}): ").strip()
                    if not choice:
                        print("Please enter a number.")
                        continue

                    choice_num = int(choice)
                    if 1 <= choice_num <= len(calendars):
                        selected_calendar = calendars[choice_num - 1]
                        target_calendar = selected_calendar['id']
                        selected_calendar_name = selected_calendar['name']
                        primary_note = " (Primary)" if selected_calendar['primary'] else ""
                        print(f"✅ Selected calendar: {selected_calendar_name}{primary_note}")
                        break
                    else:
                        print(f"Please enter a number between 1 and {len(calendars)}.")
                except ValueError:
                    print("Please enter a valid number.")
                except KeyboardInterrupt:
                    print("\n❌ Setup cancelled by user.")
                    return False
        
        # Add events
        from academic_calendar_events import get_academic_calendar_events
        events = get_academic_calendar_events()
        print(f"\nAdding {len(events)} events to '{selected_calendar_name}'...")

        results = integration.add_all_events(events, target_calendar)

        print(f"\n✅ Successfully added: {results['successful']} events to '{selected_calendar_name}'")
        if results['failed'] > 0:
            print(f"❌ Failed to add: {results['failed']} events")
            for error in results['errors']:
                print(f"   - {error}")

        print(f"\n🎉 Academic Calendar Google Calendar integration complete!")
        print(f"📅 Calendar: {selected_calendar_name}")
        print(f"📊 Events added: {results['successful']}")
        print(f"📱 Events will sync across all your devices")

        return True

    except Exception as e:
        print(f"❌ Error setting up Academic Calendar Google Calendar: {e}")
        return False


# Backward compatibility functions
def setup_google_calendar():
    """Backward compatibility function."""
    return setup_academic_calendar_google_integration()


class GoogleCalendarIntegration(AcademicCalendarGoogleIntegration):
    """Backward compatibility class."""
    pass


if __name__ == "__main__":
    setup_academic_calendar_google_integration()
