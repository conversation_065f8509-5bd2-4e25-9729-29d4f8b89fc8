#!/usr/bin/env python3
"""
Integrated Academic View
Combines academic calendar events with personal course schedule for a comprehensive view.
"""

from datetime import datetime, date
from typing import List, Dict, Optional
from academic_calendar_events import get_academic_calendar_events, AcademicCalendarEvent
from personal_course_schedule import get_personal_course_schedule, PersonalCourse, CourseStatus
from academic_calendar import AcademicCalendarApp


class IntegratedAcademicView:
    """Provides an integrated view of academic calendar and personal course schedule."""
    
    def __init__(self):
        self.calendar_app = AcademicCalendarApp()
        self.academic_events = get_academic_calendar_events()
        self.personal_courses = get_personal_course_schedule()
    
    def get_events_for_term(self, term: str) -> Dict[str, List]:
        """Get both academic events and personal courses for a specific term."""
        # Get academic events for the term
        academic_events = self.calendar_app.filter_events(term=term)
        
        # Get personal courses for the term
        personal_courses = [course for course in self.personal_courses 
                          if term.lower() in course.term.lower()]
        
        return {
            'academic_events': academic_events,
            'personal_courses': personal_courses,
            'term': term
        }
    
    def display_term_overview(self, term: str):
        """Display a comprehensive overview for a specific term."""
        data = self.get_events_for_term(term)
        
        print(f"📅 {term} - Comprehensive Overview")
        print("=" * 60)
        
        # Personal Courses Section
        if data['personal_courses']:
            print(f"\n📚 Your Courses ({len(data['personal_courses'])} courses):")
            for course in data['personal_courses']:
                print(f"  {course}")
        else:
            print(f"\n📚 Your Courses: No courses scheduled for {term}")
        
        # Academic Events Section
        if data['academic_events']:
            print(f"\n📋 Academic Calendar Events ({len(data['academic_events'])} events):")
            
            # Group events by category
            events_by_category = {}
            for event in data['academic_events']:
                category = event.category.value
                if category not in events_by_category:
                    events_by_category[category] = []
                events_by_category[category].append(event)
            
            for category in sorted(events_by_category.keys()):
                events = events_by_category[category]
                print(f"\n  📌 {category.title()} ({len(events)} events):")
                for event in sorted(events, key=lambda x: x.date):
                    print(f"    • {event.title} - {event.date.strftime('%B %d, %Y')}")
        else:
            print(f"\n📋 Academic Calendar Events: No events found for {term}")
    
    def display_all_terms_summary(self):
        """Display a summary of all terms with courses and events."""
        print("🎓 Complete Academic Overview")
        print("=" * 60)
        
        # Get all unique terms from both courses and events
        course_terms = set(course.term for course in self.personal_courses)
        event_terms = set(event.term for event in self.academic_events)
        all_terms = sorted(course_terms.union(event_terms))
        
        print(f"\n📊 Summary Statistics:")
        print(f"  Total Terms: {len(all_terms)}")
        print(f"  Total Courses: {len(self.personal_courses)}")
        print(f"  Total Academic Events: {len(self.academic_events)}")
        
        print(f"\n📅 Terms Overview:")
        for term in all_terms:
            data = self.get_events_for_term(term)
            course_count = len(data['personal_courses'])
            event_count = len(data['academic_events'])
            
            course_status = ""
            if data['personal_courses']:
                statuses = [c.status.value for c in data['personal_courses']]
                course_status = f" ({', '.join(set(statuses))})"
            
            print(f"  📅 {term}:")
            print(f"    Courses: {course_count}{course_status}")
            print(f"    Events: {event_count}")
    
    def get_upcoming_deadlines_with_courses(self, days: int = 30) -> List[Dict]:
        """Get upcoming deadlines with context about related courses."""
        upcoming_deadlines = self.calendar_app.get_upcoming_deadlines(days)
        
        results = []
        for deadline in upcoming_deadlines:
            # Find related courses for the same term
            related_courses = [course for course in self.personal_courses 
                             if course.term == deadline.term]
            
            results.append({
                'deadline': deadline,
                'related_courses': related_courses,
                'days_until': (deadline.date - date.today()).days
            })
        
        return results
    
    def display_upcoming_deadlines_with_context(self, days: int = 30):
        """Display upcoming deadlines with course context."""
        deadlines_with_context = self.get_upcoming_deadlines_with_courses(days)
        
        print(f"⏰ Upcoming Deadlines (next {days} days)")
        print("=" * 50)
        
        if not deadlines_with_context:
            print("No upcoming deadlines found.")
            return
        
        for item in deadlines_with_context:
            deadline = item['deadline']
            related_courses = item['related_courses']
            days_until = item['days_until']
            
            print(f"\n📅 {deadline.title}")
            print(f"   Date: {deadline.date.strftime('%A, %B %d, %Y')} ({days_until} days)")
            print(f"   Term: {deadline.term}")
            print(f"   Category: {deadline.category.value.title()}")
            
            if related_courses:
                print(f"   Your Courses in {deadline.term}:")
                for course in related_courses:
                    print(f"     • {course.course_code}: {course.course_title} ({course.status.value})")
            else:
                print(f"   No personal courses scheduled for {deadline.term}")


def main():
    """Main function to demonstrate the integrated view."""
    view = IntegratedAcademicView()
    
    print("🎓 Academic Calendar & Personal Course Integration")
    print("=" * 60)
    
    # Display overall summary
    view.display_all_terms_summary()
    
    # Display detailed view for each term with courses
    course_terms = set(course.term for course in view.personal_courses)
    
    for term in sorted(course_terms):
        print("\n" + "="*60)
        view.display_term_overview(term)
    
    # Display upcoming deadlines with course context
    print("\n" + "="*60)
    view.display_upcoming_deadlines_with_context(90)  # Next 90 days


if __name__ == "__main__":
    main()
