# 📥 Calendar Import Functionality Guide

## 🎯 **Overview**

The Academic Calendar application now supports importing calendar events from external files, allowing you to integrate events from other calendar systems, spreadsheets, or calendar applications.

## 🔧 **Supported File Formats**

### **1. iCal Files (.ics)**
- Standard calendar format used by most calendar applications
- Supports all standard iCal properties (SUMMARY, DTSTART, DESCRIPTION, etc.)
- Automatically parses event categories and reminders
- Compatible with exports from Google Calendar, Outlook, Apple Calendar, etc.

### **2. CSV Files (.csv)**
- Comma-separated or semicolon-separated values
- Flexible column mapping (supports various column names)
- Automatic delimiter detection

**Supported CSV Column Names:**
- **Title/Event**: `title`, `summary`, `event`, `name`, `subject`
- **Date**: `date`, `start_date`, `start`, `dtstart`
- **Description**: `description`, `desc`, `details`, `notes`

## 🚀 **How to Use**

### **Method 1: Command Line**
```bash
# Import with file path
python academic_calendar.py --action import --import-file path/to/calendar.ics

# Import with interactive file browser
python academic_calendar.py --action import
```

### **Method 2: Interactive Import Wizard**
```bash
python calendar_importer.py
```

### **Method 3: Programmatic Import**
```python
from calendar_importer import CalendarImporter
from calendar_events import get_academic_events

# Load existing events
existing_events = get_academic_events()

# Import from file
importer = CalendarImporter()
imported_events, stats = importer.import_from_file('calendar.ics', existing_events)

print(f"Imported {len(imported_events)} events")
```

## 📋 **Import Process**

### **Step 1: File Selection**
- **GUI File Browser**: Automatically opens if tkinter is available
- **Command Line**: Manual file path entry with validation
- **Supported Extensions**: `.ics`, `.csv`

### **Step 2: Event Processing**
- **Parsing**: Extracts event data from file format
- **Validation**: Checks required fields (title, date)
- **Categorization**: Automatically categorizes events based on keywords
- **Duplicate Detection**: Compares with existing events by title and date

### **Step 3: Integration Options**
- **Add to Calendar**: Merge imported events with existing academic calendar
- **Regenerate Files**: Update iCal files with imported events
- **Google Calendar Sync**: Optionally sync imported events to Google Calendar

## 🔍 **Automatic Event Categorization**

The import system automatically categorizes events based on keywords in titles and descriptions:

| Keywords | Event Type | Category |
|----------|------------|----------|
| `deadline`, `last day`, `due` | Deadline | Based on context (petition, application, etc.) |
| `holiday`, `closed`, `break` | Holiday | Holiday |
| `classes begin`, `start` | Class Date | Class Start |
| `classes end`, `final day` | Class Date | Class End |
| `commencement`, `graduation` | Class Date | Commencement |
| `registration opens` | Registration | Registration |

### **Term Assignment**
Events are automatically assigned to academic terms based on date:
- **Fall 1**: August - mid September
- **Fall 2**: mid September - December
- **Spring 1**: November - February
- **Spring 2**: February - March
- **Summer**: April - July

## 📊 **Import Statistics**

After each import, you'll see a detailed summary:

```
📊 Import Summary
====================
Total events processed: 15
✅ Successfully imported: 12
⏭️  Duplicates skipped: 2
❌ Failed imports: 1

🚨 Errors encountered:
  - Failed to parse date for event "Invalid Event"
```

## 🛡️ **Error Handling**

### **Common Issues and Solutions**

| Issue | Cause | Solution |
|-------|-------|----------|
| **File not found** | Incorrect file path | Verify file exists and path is correct |
| **Unsupported format** | Wrong file extension | Use `.ics` or `.csv` files only |
| **Date parsing failed** | Invalid date format | Use standard date formats (YYYY-MM-DD, MM/DD/YYYY, etc.) |
| **Missing required fields** | No title or date | Ensure CSV has title and date columns |
| **Encoding issues** | Special characters | File will try UTF-8 then Latin-1 encoding |

### **Supported Date Formats**
- `2025-08-01` (ISO format)
- `08/01/2025` (US format)
- `01/08/2025` (European format)
- `August 01, 2025` (Long format)
- `Aug 01, 2025` (Short format)
- `20250801` (Compact format)
- `2025-08-01T12:00:00` (ISO with time)

## 📁 **Sample Files**

### **Sample CSV Format**
```csv
title,date,description
"Final Exam Period","2025-12-15","Start of final examinations"
"Winter Break","2025-12-20","University winter break begins"
"Spring Registration","2026-01-10","Registration opens for Spring"
```

### **Sample iCal Export**
Most calendar applications can export to iCal format:
- **Google Calendar**: Settings → Import & Export → Export
- **Outlook**: File → Save Calendar → iCalendar Format
- **Apple Calendar**: File → Export → Export

## 🔄 **Integration Workflow**

### **Complete Import and Integration**
1. **Import Events**: `python academic_calendar.py --action import`
2. **Review Results**: Check import summary and preview
3. **Add to Calendar**: Choose to merge with existing events
4. **Regenerate Files**: Update all iCal files
5. **Sync to Google**: Optionally sync to Google Calendar
6. **Verify**: Check calendar applications for new events

### **Batch Processing**
```bash
# Import multiple files
python academic_calendar.py --action import --import-file file1.csv
python academic_calendar.py --action import --import-file file2.ics
python academic_calendar.py --action import --import-file file3.csv

# Regenerate all files after imports
python academic_calendar.py --action generate
```

## 🧪 **Testing Import Functionality**

Run the comprehensive test suite:
```bash
python test_import_functionality.py
```

Tests include:
- ✅ iCal file import
- ✅ CSV file import  
- ✅ Duplicate detection
- ✅ Error handling
- ✅ Date format parsing
- ✅ Event categorization

## 💡 **Best Practices**

### **Before Importing**
1. **Backup**: Export current calendar before importing
2. **Clean Data**: Review external file for accuracy
3. **Test Small**: Import a few events first to verify format

### **During Import**
1. **Review Preview**: Check categorization and dates
2. **Handle Duplicates**: System automatically skips duplicates
3. **Check Errors**: Review any failed imports

### **After Import**
1. **Verify Events**: Check calendar applications
2. **Update Reminders**: Ensure reminders are set correctly
3. **Share Files**: Distribute updated iCal files if needed

## 🔧 **Advanced Usage**

### **Custom Event Processing**
Modify `calendar_importer.py` to customize:
- Event categorization logic
- Date parsing formats
- Term assignment rules
- Duplicate detection criteria

### **Bulk Import Script**
```python
from calendar_importer import CalendarImporter
from calendar_events import get_academic_events
import glob

# Import all CSV files in a directory
importer = CalendarImporter()
existing_events = get_academic_events()
all_imported = []

for file_path in glob.glob("import_files/*.csv"):
    events, stats = importer.import_from_file(file_path, existing_events)
    all_imported.extend(events)
    print(f"Imported {len(events)} from {file_path}")

print(f"Total imported: {len(all_imported)} events")
```

## 🎉 **Success Metrics**

The import functionality provides:
- ✅ **Universal Compatibility**: Works with any calendar application
- ✅ **Smart Processing**: Automatic categorization and term assignment
- ✅ **Duplicate Prevention**: Avoids importing the same event twice
- ✅ **Error Recovery**: Graceful handling of malformed data
- ✅ **Integration Options**: Flexible workflow for different use cases
- ✅ **Comprehensive Testing**: Validated with extensive test suite

**Your academic calendar system now supports seamless integration with external calendar sources!**
