# 🚀 Quick Google Calendar Setup Reference

## ⚡ **Fast Track Setup** (5 minutes)

### **1. Google Cloud Console Setup**
🌐 **Already opened**: https://console.cloud.google.com/

**Quick Steps:**
1. **Create Project**: Click project dropdown → "New Project" → Name: "Academic Calendar" → Create
2. **Enable API**: Search "Google Calendar API" → Enable
3. **OAuth Screen**: APIs & Services → OAuth consent → External → Fill basic info → Save
4. **Credentials**: APIs & Services → Credentials → Create → OAuth 2.0 Client ID → Desktop → Download JSON

### **2. File Setup**
```bash
# 1. Rename downloaded file to 'credentials.json'
# 2. Place in your project directory: /Users/<USER>/Documents/augment-projects/Tasks/
```

### **3. Run Setup**
```bash
# Activate virtual environment
source calendar_env/bin/activate

# Run setup checker
python setup_google_calendar.py
```

---

## 🔗 **Direct Links** (Open in new tabs)

| Step | Direct Link | Action |
|------|-------------|---------|
| 1 | [Create Project](https://console.cloud.google.com/projectcreate) | Create new project |
| 2 | [API Library](https://console.cloud.google.com/apis/library) | Search "Google Calendar API" |
| 3 | [OAuth Consent](https://console.cloud.google.com/apis/credentials/consent) | Configure consent screen |
| 4 | [Credentials](https://console.cloud.google.com/apis/credentials) | Create OAuth 2.0 Client ID |

---

## ✅ **Checklist**

- [ ] Google Cloud project created
- [ ] Google Calendar API enabled  
- [ ] OAuth consent screen configured
- [ ] OAuth 2.0 credentials created
- [ ] `credentials.json` downloaded and placed in project directory
- [ ] Virtual environment activated
- [ ] Setup script run successfully

---

## 🎯 **What You'll Get**

After setup completion:
- **13 academic events** automatically added to Google Calendar
- **Smart reminders**: 7 days + 1 day before deadlines
- **Cross-device sync**: Available on phone, tablet, computer
- **Professional formatting**: Proper categories and descriptions

---

## 🆘 **Need Help?**

**Common Issues:**
- **"API not enabled"** → Wait 2-3 minutes after enabling
- **"Access blocked"** → Add your email as test user in OAuth consent
- **"File not found"** → Check filename is exactly `credentials.json`

**Detailed Guide:** See `GOOGLE_CALENDAR_SETUP.md`

---

**Ready?** Follow the steps above, then run: `python setup_google_calendar.py`
