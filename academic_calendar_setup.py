#!/usr/bin/env python3
"""
Interactive Academic Calendar Google Calendar Setup Script
Guides you through the complete setup process.
"""

import os
import sys
import json
from pathlib import Path


def check_virtual_environment():
    """Check if we're in the virtual environment."""
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  You're not in the virtual environment!")
        print("\nTo activate the virtual environment, run:")
        print("source calendar_env/bin/activate")
        print("\nThen run this script again.")
        return False
    return True


def check_google_packages():
    """Check if Google Calendar packages are installed."""
    try:
        import google.auth
        import google_auth_oauthlib
        import googleapiclient
        print("✅ Google Calendar packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing Google packages: {e}")
        print("\nInstall with:")
        print("pip install google-auth google-auth-oauthlib google-api-python-client")
        return False


def check_credentials_file():
    """Check if credentials.json exists and is valid."""
    creds_file = Path("credentials.json")
    
    if not creds_file.exists():
        print("❌ credentials.json file not found")
        print("\n📋 To get your credentials file:")
        print("1. Go to Google Cloud Console (https://console.cloud.google.com/)")
        print("2. Create/select a project")
        print("3. Enable Google Calendar API")
        print("4. Create OAuth 2.0 credentials")
        print("5. Download as 'credentials.json'")
        print("\n📖 See GOOGLE_CALENDAR_SETUP.md for detailed instructions")
        return False
    
    try:
        with open(creds_file, 'r') as f:
            creds_data = json.load(f)
        
        # Check if it's the right type of credentials
        if 'installed' in creds_data:
            print("✅ credentials.json file found and appears valid")
            return True
        else:
            print("❌ credentials.json doesn't appear to be OAuth 2.0 desktop credentials")
            print("Make sure you downloaded 'OAuth 2.0 Client ID' credentials, not service account")
            return False
            
    except json.JSONDecodeError:
        print("❌ credentials.json file is not valid JSON")
        return False
    except Exception as e:
        print(f"❌ Error reading credentials.json: {e}")
        return False


def run_academic_calendar_google_setup():
    """Run the Academic Calendar Google Calendar integration setup."""
    print("🚀 Starting Academic Calendar Google Calendar setup...")

    try:
        from academic_calendar_google_integration import setup_academic_calendar_google_integration
        success = setup_academic_calendar_google_integration()

        if success:
            print("\n🎉 Academic Calendar Google Calendar integration setup complete!")
            print("\nYour academic events have been added to Google Calendar with:")
            print("  ✅ Smart reminders (7 days + 1 day for deadlines)")
            print("  ✅ Proper categorization")
            print("  ✅ Sync across all your devices")
            print("\n📱 Check your Google Calendar app to see the events!")
        else:
            print("\n❌ Setup failed. Please check the error messages above.")

        return success

    except Exception as e:
        print(f"\n💥 Unexpected error during setup: {e}")
        return False


def main():
    """Main setup function."""
    print("🗓️  Academic Calendar Google Calendar Integration Setup")
    print("=" * 60)

    # Check virtual environment
    if not check_virtual_environment():
        return False

    # Check Google packages
    if not check_google_packages():
        return False

    # Check credentials file
    if not check_credentials_file():
        return False

    # All checks passed, proceed with setup
    print("\n✅ All prerequisites met!")

    # Ask user if they want to proceed
    proceed = input("\n🚀 Ready to set up Academic Calendar Google Calendar integration? (y/n): ").lower().strip()

    if proceed in ['y', 'yes']:
        return run_academic_calendar_google_setup()
    else:
        print("\n👋 Setup cancelled. Run this script again when you're ready!")
        return False


# Backward compatibility functions
def run_google_calendar_setup():
    """Backward compatibility function."""
    return run_academic_calendar_google_setup()


if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n📖 For detailed setup instructions, see:")
        print("   - GOOGLE_CALENDAR_SETUP.md")
        print("   - README.md")
    
    sys.exit(0 if success else 1)
