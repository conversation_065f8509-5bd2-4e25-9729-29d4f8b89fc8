# ⚡ Academic Calendar Application - Quick Reference Guide

## 🚀 **Command Cheat Sheet**

### **Basic Commands**
| Command | Description |
|---------|-------------|
| `python academic_calendar.py` | Display all events (table format) |
| `python academic_calendar.py --help` | Show all available options |
| `python academic_calendar.py --format summary` | Show event statistics |
| `python academic_calendar.py --format list` | Show detailed event list |

### **Calendar Management**
| Command | Description |
|---------|-------------|
| `python academic_calendar.py --action generate` | Generate all calendar files |
| `python academic_calendar.py --action import --import-file file.csv` | Import events from file |
| `python academic_calendar.py --action import` | Interactive import wizard |
| `python academic_calendar.py --action upcoming` | Show upcoming deadlines (7 days) |
| `python academic_calendar.py --action upcoming --days 30` | Show upcoming deadlines (30 days) |

### **Filtering Commands**
| Command | Description |
|---------|-------------|
| `python academic_calendar.py --type deadline` | Show only deadlines |
| `python academic_calendar.py --type class_date` | Show only class dates |
| `python academic_calendar.py --type holiday` | Show only holidays |
| `python academic_calendar.py --type registration` | Show only registration events |
| `python academic_calendar.py --term "Fall 1"` | Show Fall 1 events (partial match) |
| `python academic_calendar.py --term "Fall 1 - 2025"` | Show exact term match |
| `python academic_calendar.py --category petition` | Show petition deadlines |
| `python academic_calendar.py --category registration` | Show registration events |

### **Combined Filters**
| Command | Description |
|---------|-------------|
| `python academic_calendar.py --term "Fall 1" --type deadline` | Fall 1 deadlines only |
| `python academic_calendar.py --term "Spring" --category registration` | Spring registration events |
| `python academic_calendar.py --type holiday --format list` | Detailed holiday list |

### **Personal Schedule**
| Command | Description |
|---------|-------------|
| `python personal_course_schedule.py` | View your course schedule |
| `python integrated_academic_view.py` | Comprehensive academic overview |

### **Google Calendar Integration**
| Command | Description |
|---------|-------------|
| `python academic_calendar_setup.py` | Setup Google Calendar integration |
| `python academic_calendar_test_oauth.py` | Test Google authentication |

### **Testing and Validation**
| Command | Description |
|---------|-------------|
| `python academic_calendar_test.py` | Test core functionality |
| `python academic_calendar_test_import.py` | Test import functionality |
| `python filter_validation_test.py` | Validate all filters |

---

## 🔍 **Filter Syntax Examples**

### **Event Type Filters**
```bash
# Valid event types
--type deadline        # Academic deadlines
--type class_date      # Class-related dates  
--type holiday         # University holidays
--type registration    # Registration periods
```

### **Term Filters (Case Insensitive)**
```bash
# Exact matches
--term "Fall 1 - 2025"
--term "Spring 2 - 2026"
--term "Summer 2026"

# Partial matches
--term "Fall"          # All Fall terms
--term "spring"        # All Spring terms (case insensitive)
--term "2026"          # All 2026 terms
--term "1"             # All "1" terms (Fall 1, Spring 1)
```

### **Category Filters**
```bash
# Academic process categories
--category petition        # Graduation petitions
--category application     # Application deadlines
--category registration    # Registration events
--category payment         # Payment deadlines

# Academic calendar categories
--category class_start     # Class beginning dates
--category class_end       # Class ending dates
--category withdrawal      # Withdrawal deadlines
--category holiday         # Holiday events
--category commencement    # Graduation ceremonies
```

### **Complex Filter Examples**
```bash
# Fall 1 registration deadlines
python academic_calendar.py --term "Fall 1" --type deadline --category registration

# All Spring holidays
python academic_calendar.py --term "Spring" --type holiday

# 2026 commencement events
python academic_calendar.py --term "2026" --category commencement

# Upcoming payment deadlines
python academic_calendar.py --action upcoming --days 60 | grep -i payment
```

---

## 📄 **File Format Specifications**

### **CSV Import Format**
```csv
title,date,description,event_type,category,term
"Event Title","YYYY-MM-DD","Description","event_type","category","Term Name"
```

#### **Required Fields**
- **title**: Event name (string)
- **date**: ISO date format YYYY-MM-DD
- **event_type**: One of: `deadline`, `class_date`, `holiday`, `registration`
- **category**: One of: `petition`, `application`, `registration`, `payment`, `class_start`, `withdrawal`, `holiday`, `class_end`, `commencement`
- **term**: Academic term identifier

#### **Optional Fields**
- **description**: Detailed event description

#### **Example CSV File**
```csv
title,date,description,event_type,category,term
"Final Exams Begin","2025-12-15","Start of final examination period","class_date","class_end","Fall 2 - 2025"
"Registration Opens","2025-11-01","Registration opens for Spring term","registration","registration","Spring 1 - 2026"
"Thanksgiving Break","2025-11-28","University closed for Thanksgiving","holiday","holiday","Fall 2 - 2025"
"Payment Deadline","2025-08-15","Final payment deadline","deadline","payment","Fall 1 - 2025"
```

### **iCal Export Format**
- **Standard**: RFC 5545 iCalendar specification
- **Encoding**: UTF-8
- **Timezone**: America/Los_Angeles (configurable)
- **Reminders**: Automatic based on event type
- **Categories**: Event type and category included

---

## 🎯 **Common Workflow Examples**

### **Daily Academic Planning**
```bash
# 1. Check upcoming deadlines
python academic_calendar.py --action upcoming --days 7

# 2. View current term events
python academic_calendar.py --term "Fall 1 - 2025" --format list

# 3. Check your course schedule
python personal_course_schedule.py
```

### **Term Preparation**
```bash
# 1. View all events for upcoming term
python academic_calendar.py --term "Spring 1 - 2026"

# 2. Focus on registration deadlines
python academic_calendar.py --term "Spring 1" --category registration

# 3. Generate calendar file for import
python academic_calendar.py --action generate
```

### **Academic Year Overview**
```bash
# 1. Get comprehensive overview
python integrated_academic_view.py

# 2. View all deadlines by term
python academic_calendar.py --type deadline --format list

# 3. Check holiday schedule
python academic_calendar.py --type holiday
```

### **Import External Events**
```bash
# 1. Prepare CSV file with required format
# 2. Import events
python academic_calendar.py --action import --import-file myevents.csv

# 3. Regenerate calendar files
python academic_calendar.py --action generate

# 4. Verify import
python academic_calendar.py --format summary
```

### **Google Calendar Setup**
```bash
# 1. Get Google API credentials (credentials.json)
# 2. Run setup wizard
python academic_calendar_setup.py

# 3. Test authentication
python academic_calendar_test_oauth.py

# 4. Verify events in Google Calendar
```

---

## 📊 **System Statistics**

### **Current Data (2025-2026 Academic Year)**
- **Total Events**: 50
- **Academic Terms**: 6 (Fall 1-2025, Fall 2-2025, Spring 1-2026, Spring 2-2026, Summer 2026, Fall 1-2026)
- **Event Types**: 4 (deadline, class_date, holiday, registration)
- **Categories**: 9 (petition, application, registration, payment, class_start, withdrawal, holiday, class_end, commencement)

### **Event Distribution**
| Event Type | Count | Categories |
|------------|-------|------------|
| **Deadline** | 28 | petition, application, registration, payment, withdrawal |
| **Class Date** | 10 | class_start, class_end, commencement |
| **Holiday** | 8 | holiday |
| **Registration** | 4 | registration |

### **Term Distribution**
| Term | Events | Your Courses |
|------|--------|--------------|
| **Fall 1 - 2025** | 10 | CIS 5600 (registered) |
| **Fall 2 - 2025** | 13 | CIS 5410 (planned) |
| **Spring 1 - 2026** | 10 | CIS 5420 (planned) |
| **Spring 2 - 2026** | 13 | CIS 5898 (planned) |
| **Summer 2026** | 3 | None |
| **Fall 1 - 2026** | 1 | None |

---

## 🔧 **Troubleshooting Quick Fixes**

### **Common Issues**
| Problem | Quick Fix |
|---------|-----------|
| "ModuleNotFoundError" | `source calendar_env/bin/activate` |
| "No events found" | Check filter spelling, use partial matching |
| Google Calendar fails | Verify `credentials.json` exists |
| Import fails | Check CSV format and column names |
| Wrong event count | Run `python filter_validation_test.py` |

### **Validation Commands**
```bash
# Check total event count (should be 50)
python academic_calendar.py --format summary

# Validate all filters
python filter_validation_test.py

# Test core functionality
python academic_calendar_test.py

# Test import functionality
python academic_calendar_test_import.py
```

### **Reset Commands**
```bash
# Regenerate all calendar files
python academic_calendar.py --action generate

# Reset Google Calendar authentication
rm token.json && python academic_calendar_setup.py

# Verify system integrity
python academic_calendar_test.py
```

---

## 📱 **Integration Quick Start**

### **Apple Calendar**
1. Generate calendar: `python academic_calendar.py --action generate`
2. Double-click any `.ics` file
3. Choose destination calendar
4. Click "OK"

### **Google Calendar (Web)**
1. Go to Google Calendar → Settings → Import & Export
2. Click "Select file from your computer"
3. Choose `.ics` file
4. Select destination calendar
5. Click "Import"

### **Microsoft Outlook**
1. File → Open & Export → Import/Export
2. Select "Import an iCalendar (.ics) or vCalendar file"
3. Browse and select `.ics` file
4. Choose destination calendar

### **Google Calendar (API Integration)**
1. Get credentials: [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Google Calendar API
3. Download `credentials.json`
4. Run: `python academic_calendar_setup.py`

---

## 🎓 **Academic Success Tips**

### **Stay Organized**
- Run `python academic_calendar.py --action upcoming` weekly
- Import calendar files to your preferred calendar app
- Use `python integrated_academic_view.py` for term planning

### **Never Miss Deadlines**
- Set up Google Calendar integration for automatic reminders
- Check `--category registration` and `--category payment` regularly
- Use `--action upcoming --days 30` for monthly planning

### **Track Your Progress**
- Update `personal_course_schedule.py` as you register for courses
- Use term-specific filters to focus on relevant events
- Generate fresh calendar files each term

**Quick Reference Complete - Your Academic Calendar application is ready for efficient academic planning!** ⚡
